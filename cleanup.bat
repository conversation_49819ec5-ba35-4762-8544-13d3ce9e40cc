@echo off
chcp 65001 >nul
echo ========================================
echo Arduino Controller - Cleanup
echo تنظيف بيئة تحكم Arduino
echo ========================================
echo.

echo This will remove the virtual environment and all installed packages.
echo سيتم حذف البيئة الافتراضية وجميع المكتبات المثبتة.
echo.
set /p confirm="Are you sure? (y/N) / هل أنت متأكد؟ (y/N): "

if /i "%confirm%" neq "y" (
    echo Cleanup cancelled.
    echo تم إلغاء التنظيف.
    pause
    exit /b 0
)

echo.
echo Removing virtual environment...
echo حذف البيئة الافتراضية...

if exist "arduino_env" (
    rmdir /s /q "arduino_env"
    echo ✓ Virtual environment removed
    echo ✓ تم حذف البيئة الافتراضية
) else (
    echo Virtual environment not found
    echo البيئة الافتراضية غير موجودة
)

echo.
echo Removing temporary files...
echo حذف الملفات المؤقتة...

if exist "arduino_gui_settings.json" (
    del "arduino_gui_settings.json"
    echo ✓ Settings file removed
    echo ✓ تم حذف ملف الإعدادات
)

if exist "__pycache__" (
    rmdir /s /q "__pycache__"
    echo ✓ Python cache removed
    echo ✓ تم حذف ذاكرة Python المؤقتة
)

if exist "*.pyc" (
    del "*.pyc"
    echo ✓ Python compiled files removed
    echo ✓ تم حذف ملفات Python المترجمة
)

echo.
echo ========================================
echo Cleanup completed!
echo تم التنظيف!
echo ========================================
echo.
echo To reinstall, run: setup_environment.bat
echo لإعادة التثبيت، شغل: setup_environment.bat
echo.
pause
