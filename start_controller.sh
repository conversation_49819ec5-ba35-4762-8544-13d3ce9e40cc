#!/bin/bash

echo "========================================"
echo "Arduino Serial Controller"
echo "تحكم Arduino عبر السيريال"
echo "========================================"
echo

# Check if virtual environment exists
if [ ! -f "arduino_env/bin/activate" ]; then
    echo "Virtual environment not found!"
    echo "البيئة الافتراضية غير موجودة!"
    echo "Please run setup_linux.sh first"
    echo "يرجى تشغيل setup_linux.sh أولاً"
    echo
    exit 1
fi

echo "Activating virtual environment..."
echo "تفعيل البيئة الافتراضية..."
source arduino_env/bin/activate

echo "Starting Arduino Controller GUI..."
echo "بدء تشغيل واجهة تحكم Arduino..."
echo
python arduino_gui.py

echo
echo "Application closed."
echo "تم إغلاق التطبيق."
