@echo off
chcp 65001 >nul
echo ========================================
echo Arduino Serial Controller Setup
echo إعداد بيئة تحكم Arduino عبر السيريال
echo ========================================
echo.

echo [1/5] Creating virtual environment...
echo إنشاء البيئة الافتراضية...
python -m venv arduino_env
if %errorlevel% neq 0 (
    echo Error: Failed to create virtual environment
    echo خطأ: فشل في إنشاء البيئة الافتراضية
    pause
    exit /b 1
)

echo [2/5] Activating virtual environment...
echo تفعيل البيئة الافتراضية...
call arduino_env\Scripts\activate.bat

echo [3/5] Upgrading pip...
echo تحديث pip...
python -m pip install --upgrade pip

echo [4/5] Installing required packages...
echo تثبيت المكتبات المطلوبة...
pip install pyserial

echo [5/5] Testing installation...
echo اختبار التثبيت...
python -c "import serial; print('✓ pyserial installed successfully')"
if %errorlevel% neq 0 (
    echo Error: Installation failed
    echo خطأ: فشل التثبيت
    pause
    exit /b 1
)

echo.
echo ========================================
echo Setup completed successfully!
echo تم الإعداد بنجاح!
echo ========================================
echo.
echo To run the application:
echo لتشغيل التطبيق:
echo 1. Run: start_controller.bat
echo 2. Or manually: arduino_env\Scripts\activate.bat then python arduino_gui.py
echo.
pause
