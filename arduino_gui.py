#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Arduino Serial Controller GUI
واجهة تحكم Arduino عبر السيريال
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import serial
import serial.tools.list_ports
import threading
import time
import json
import os

class ArduinoController:
    def __init__(self, root):
        self.root = root
        self.root.title("Arduino Serial Controller - تحكم Arduino عبر السيريال")
        self.root.geometry("1000x700")
        self.root.configure(bg='#f0f0f0')
        
        # Serial connection
        self.serial_connection = None
        self.is_connected = False
        self.auto_refresh = False
        
        # Data storage
        self.pwm_values = [0, 0, 0]
        self.stepper_data = {"angle": 0.0, "speed": 12, "mode": "IDLE"}
        self.relay_data = {
            "right": {"active": False, "timer": 5, "remaining": 0},
            "left": {"active": False, "timer": 5, "remaining": 0}
        }
        self.shoot_rate = 1
        self.continuous_shooting = False
        
        # Settings file
        self.settings_file = "arduino_gui_settings.json"
        self.load_settings()
        
        self.create_widgets()
        self.refresh_ports()
        
        # Auto refresh timer
        self.auto_refresh_timer()
        
    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Connection frame
        self.create_connection_frame(main_frame)
        
        # Control tabs
        self.create_control_tabs(main_frame)
        
        # Status and log frame
        self.create_status_frame(main_frame)
        
    def create_connection_frame(self, parent):
        conn_frame = ttk.LabelFrame(parent, text="اتصال السيريال - Serial Connection", padding="5")
        conn_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Port selection
        ttk.Label(conn_frame, text="المنفذ - Port:").grid(row=0, column=0, padx=(0, 5))
        self.port_var = tk.StringVar()
        self.port_combo = ttk.Combobox(conn_frame, textvariable=self.port_var, width=15)
        self.port_combo.grid(row=0, column=1, padx=(0, 5))
        
        # Baud rate
        ttk.Label(conn_frame, text="سرعة البيانات - Baud:").grid(row=0, column=2, padx=(5, 5))
        self.baud_var = tk.StringVar(value="115200")
        baud_combo = ttk.Combobox(conn_frame, textvariable=self.baud_var, width=10, 
                                 values=["9600", "57600", "115200"])
        baud_combo.grid(row=0, column=3, padx=(0, 5))
        
        # Buttons
        self.refresh_btn = ttk.Button(conn_frame, text="تحديث المنافذ", command=self.refresh_ports)
        self.refresh_btn.grid(row=0, column=4, padx=(5, 5))
        
        self.connect_btn = ttk.Button(conn_frame, text="اتصال", command=self.toggle_connection)
        self.connect_btn.grid(row=0, column=5, padx=(5, 0))
        
        # Status indicator
        self.status_label = ttk.Label(conn_frame, text="غير متصل", foreground="red")
        self.status_label.grid(row=0, column=6, padx=(10, 0))
        
    def create_control_tabs(self, parent):
        # Notebook for tabs
        self.notebook = ttk.Notebook(parent)
        self.notebook.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # PWM Control Tab
        self.create_pwm_tab()
        
        # Shooting Control Tab
        self.create_shooting_tab()
        
        # Stepper Motor Tab
        self.create_stepper_tab()
        
        # Relay Control Tab
        self.create_relay_tab()
        
        # Settings Tab
        self.create_settings_tab()
        
    def create_pwm_tab(self):
        pwm_frame = ttk.Frame(self.notebook)
        self.notebook.add(pwm_frame, text="تحكم PWM")
        
        # PWM channels
        self.pwm_vars = []
        self.pwm_scales = []
        self.pwm_labels = []
        
        channels = [
            ("الأحمر - Red (D9)", "#ff0000"),
            ("الأزرق - Blue (D6)", "#0000ff"), 
            ("الأخضر - Green (D5)", "#00ff00")
        ]
        
        for i, (name, color) in enumerate(channels):
            frame = ttk.LabelFrame(pwm_frame, text=name, padding="10")
            frame.grid(row=i, column=0, sticky=(tk.W, tk.E), padx=10, pady=5)
            frame.columnconfigure(1, weight=1)
            
            # Value variable
            var = tk.IntVar(value=self.pwm_values[i])
            self.pwm_vars.append(var)
            
            # Scale
            scale = tk.Scale(frame, from_=0, to=255, orient=tk.HORIZONTAL, variable=var,
                           command=lambda val, ch=i: self.on_pwm_change(ch, val))
            scale.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 10))
            self.pwm_scales.append(scale)
            
            # Value label
            label = ttk.Label(frame, text=f"0 (0.00V)")
            label.grid(row=0, column=2, padx=(10, 0))
            self.pwm_labels.append(label)
            
            # Quick buttons
            btn_frame = ttk.Frame(frame)
            btn_frame.grid(row=1, column=0, columnspan=3, pady=(5, 0))
            
            ttk.Button(btn_frame, text="MIN", command=lambda ch=i: self.set_pwm_value(ch, 0)).pack(side=tk.LEFT, padx=2)
            ttk.Button(btn_frame, text="25%", command=lambda ch=i: self.set_pwm_value(ch, 64)).pack(side=tk.LEFT, padx=2)
            ttk.Button(btn_frame, text="50%", command=lambda ch=i: self.set_pwm_value(ch, 128)).pack(side=tk.LEFT, padx=2)
            ttk.Button(btn_frame, text="75%", command=lambda ch=i: self.set_pwm_value(ch, 192)).pack(side=tk.LEFT, padx=2)
            ttk.Button(btn_frame, text="MAX", command=lambda ch=i: self.set_pwm_value(ch, 255)).pack(side=tk.LEFT, padx=2)
        
        # Update PWM display
        self.update_pwm_display()
        
    def create_shooting_tab(self):
        shoot_frame = ttk.Frame(self.notebook)
        self.notebook.add(shoot_frame, text="تحكم النبضات")
        
        # Single shot
        single_frame = ttk.LabelFrame(shoot_frame, text="نبضة واحدة - Single Shot", padding="10")
        single_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=10, pady=5)
        
        ttk.Button(single_frame, text="إرسال نبضة واحدة", command=self.single_shoot,
                  style="Accent.TButton").pack(pady=5)
        
        # Continuous shooting
        cont_frame = ttk.LabelFrame(shoot_frame, text="نبضات مستمرة - Continuous Shooting", padding="10")
        cont_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=10, pady=5)
        
        # Rate control
        rate_frame = ttk.Frame(cont_frame)
        rate_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(rate_frame, text="معدل النبضات (نبضة/ثانية):").pack(side=tk.LEFT)
        self.rate_var = tk.IntVar(value=self.shoot_rate)
        rate_scale = tk.Scale(rate_frame, from_=0, to=10, orient=tk.HORIZONTAL, 
                             variable=self.rate_var, command=self.on_rate_change)
        rate_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 10))
        
        self.rate_label = ttk.Label(rate_frame, text=f"{self.shoot_rate} Hz")
        self.rate_label.pack(side=tk.LEFT)
        
        # Start/Stop buttons
        btn_frame = ttk.Frame(cont_frame)
        btn_frame.pack(fill=tk.X)
        
        self.start_shoot_btn = ttk.Button(btn_frame, text="بدء النبضات المستمرة", 
                                         command=self.start_continuous_shooting)
        self.start_shoot_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_shoot_btn = ttk.Button(btn_frame, text="إيقاف النبضات", 
                                        command=self.stop_continuous_shooting)
        self.stop_shoot_btn.pack(side=tk.LEFT)
        
        # Status
        self.shoot_status_label = ttk.Label(cont_frame, text="الحالة: متوقف")
        self.shoot_status_label.pack(pady=(10, 0))
        
    def create_stepper_tab(self):
        stepper_frame = ttk.Frame(self.notebook)
        self.notebook.add(stepper_frame, text="المحرك المتدرج")
        
        # Status display
        status_frame = ttk.LabelFrame(stepper_frame, text="حالة المحرك - Motor Status", padding="10")
        status_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), padx=10, pady=5)
        
        self.stepper_status_label = ttk.Label(status_frame, text="الزاوية: 0.0° | السرعة: 12 RPM | الوضع: IDLE")
        self.stepper_status_label.pack()
        
        # Angle control
        angle_frame = ttk.LabelFrame(stepper_frame, text="التحكم بالزاوية - Angle Control", padding="10")
        angle_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=10, pady=5)
        
        angle_input_frame = ttk.Frame(angle_frame)
        angle_input_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(angle_input_frame, text="الزاوية المطلوبة (0-359):").pack(side=tk.LEFT)
        self.angle_var = tk.IntVar(value=0)
        angle_entry = ttk.Entry(angle_input_frame, textvariable=self.angle_var, width=10)
        angle_entry.pack(side=tk.LEFT, padx=(10, 10))
        
        ttk.Button(angle_input_frame, text="انتقال للزاوية", 
                  command=self.goto_angle).pack(side=tk.LEFT)
        
        # Speed control
        speed_frame = ttk.Frame(angle_frame)
        speed_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(speed_frame, text="السرعة (1-20 RPM):").pack(side=tk.LEFT)
        self.speed_var = tk.IntVar(value=self.stepper_data["speed"])
        speed_scale = tk.Scale(speed_frame, from_=1, to=20, orient=tk.HORIZONTAL,
                              variable=self.speed_var, command=self.on_speed_change)
        speed_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 10))
        
        self.speed_label = ttk.Label(speed_frame, text=f"{self.stepper_data['speed']} RPM")
        self.speed_label.pack(side=tk.LEFT)
        
        # Movement control
        move_frame = ttk.LabelFrame(stepper_frame, text="التحكم بالحركة - Movement Control", padding="10")
        move_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=10, pady=5)
        
        ttk.Button(move_frame, text="دوران مع عقارب الساعة", 
                  command=self.stepper_cw).pack(fill=tk.X, pady=2)
        ttk.Button(move_frame, text="دوران عكس عقارب الساعة", 
                  command=self.stepper_ccw).pack(fill=tk.X, pady=2)
        ttk.Button(move_frame, text="إيقاف الحركة", 
                  command=self.stepper_stop).pack(fill=tk.X, pady=2)
        ttk.Button(move_frame, text="إعادة تعيين الموضع", 
                  command=self.stepper_reset).pack(fill=tk.X, pady=2)
        
    def create_relay_tab(self):
        relay_frame = ttk.Frame(self.notebook)
        self.notebook.add(relay_frame, text="تحكم المرحلات")
        
        # Right relay
        self.create_relay_control(relay_frame, "RIGHT", "المرحل الأيمن (D10)", 0)
        
        # Left relay  
        self.create_relay_control(relay_frame, "LEFT", "المرحل الأيسر (D11)", 1)
        
    def create_relay_control(self, parent, relay_id, title, row):
        frame = ttk.LabelFrame(parent, text=title, padding="10")
        frame.grid(row=row, column=0, sticky=(tk.W, tk.E), padx=10, pady=5)
        frame.columnconfigure(1, weight=1)
        
        # Timer setting
        timer_frame = ttk.Frame(frame)
        timer_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(timer_frame, text="المؤقت (ثانية، 0 للتشغيل اليدوي):").pack(side=tk.LEFT)
        
        timer_var = tk.IntVar(value=self.relay_data[relay_id.lower()]["timer"])
        setattr(self, f"timer_{relay_id.lower()}_var", timer_var)
        
        timer_entry = ttk.Entry(timer_frame, textvariable=timer_var, width=10)
        timer_entry.pack(side=tk.LEFT, padx=(10, 0))
        
        # Control buttons
        btn_frame = ttk.Frame(frame)
        btn_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E))
        
        on_btn = ttk.Button(btn_frame, text=f"تشغيل {title.split()[0]}", 
                           command=lambda: self.relay_on(relay_id))
        on_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        off_btn = ttk.Button(btn_frame, text=f"إيقاف {title.split()[0]}", 
                            command=lambda: self.relay_off(relay_id))
        off_btn.pack(side=tk.LEFT)
        
        # Status display
        status_label = ttk.Label(frame, text="الحالة: متوقف")
        status_label.grid(row=2, column=0, columnspan=2, pady=(10, 0))
        setattr(self, f"relay_{relay_id.lower()}_status", status_label)
        
    def create_settings_tab(self):
        settings_frame = ttk.Frame(self.notebook)
        self.notebook.add(settings_frame, text="الإعدادات")
        
        # Auto refresh
        auto_frame = ttk.LabelFrame(settings_frame, text="التحديث التلقائي", padding="10")
        auto_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=10, pady=5)
        
        self.auto_refresh_var = tk.BooleanVar(value=self.auto_refresh)
        ttk.Checkbutton(auto_frame, text="تحديث تلقائي للحالة كل ثانيتين", 
                       variable=self.auto_refresh_var,
                       command=self.toggle_auto_refresh).pack()
        
        # Manual refresh
        ttk.Button(auto_frame, text="تحديث الحالة يدوياً", 
                  command=self.manual_refresh).pack(pady=(10, 0))
        
        # Save/Load settings
        save_frame = ttk.LabelFrame(settings_frame, text="حفظ الإعدادات", padding="10")
        save_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=10, pady=5)
        
        ttk.Button(save_frame, text="حفظ الإعدادات في Arduino", 
                  command=self.save_arduino_settings).pack(pady=2)
        ttk.Button(save_frame, text="إعادة تعيين Arduino للإعدادات الافتراضية", 
                  command=self.reset_arduino_settings).pack(pady=2)
        
    def create_status_frame(self, parent):
        status_frame = ttk.LabelFrame(parent, text="سجل الأحداث - Event Log", padding="5")
        status_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 0))
        status_frame.columnconfigure(0, weight=1)
        status_frame.rowconfigure(0, weight=1)
        
        # Log text area
        self.log_text = scrolledtext.ScrolledText(status_frame, height=8, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Clear log button
        ttk.Button(status_frame, text="مسح السجل", command=self.clear_log).grid(row=1, column=0, pady=(5, 0))
        
        self.log("تم تشغيل واجهة التحكم")
        
    def log(self, message):
        """Add message to log with timestamp"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        
    def clear_log(self):
        """Clear the log"""
        self.log_text.delete(1.0, tk.END)

    def refresh_ports(self):
        """Refresh available serial ports"""
        ports = [port.device for port in serial.tools.list_ports.comports()]
        self.port_combo['values'] = ports
        if ports and not self.port_var.get():
            self.port_var.set(ports[0])

    def toggle_connection(self):
        """Toggle serial connection"""
        if self.is_connected:
            self.disconnect()
        else:
            self.connect()

    def connect(self):
        """Connect to Arduino"""
        try:
            port = self.port_var.get()
            baud = int(self.baud_var.get())

            if not port:
                messagebox.showerror("خطأ", "يرجى اختيار منفذ")
                return

            self.serial_connection = serial.Serial(port, baud, timeout=1)
            time.sleep(2)  # Wait for Arduino to initialize

            self.is_connected = True
            self.connect_btn.config(text="قطع الاتصال")
            self.status_label.config(text="متصل", foreground="green")
            self.log(f"تم الاتصال بـ {port} بسرعة {baud}")

            # Get initial status
            self.manual_refresh()

        except Exception as e:
            messagebox.showerror("خطأ في الاتصال", f"فشل الاتصال: {str(e)}")
            self.log(f"فشل الاتصال: {str(e)}")

    def disconnect(self):
        """Disconnect from Arduino"""
        if self.serial_connection:
            self.serial_connection.close()
            self.serial_connection = None

        self.is_connected = False
        self.connect_btn.config(text="اتصال")
        self.status_label.config(text="غير متصل", foreground="red")
        self.log("تم قطع الاتصال")

    def send_command(self, command):
        """Send command to Arduino and return response"""
        if not self.is_connected or not self.serial_connection:
            self.log(f"خطأ: غير متصل - {command}")
            return None

        try:
            self.serial_connection.write((command + '\n').encode())
            time.sleep(0.1)  # Small delay for Arduino to process

            response = ""
            start_time = time.time()
            while time.time() - start_time < 2:  # 2 second timeout
                if self.serial_connection.in_waiting:
                    response = self.serial_connection.readline().decode().strip()
                    break
                time.sleep(0.01)

            self.log(f"أرسل: {command} | استقبل: {response}")
            return response

        except Exception as e:
            self.log(f"خطأ في الإرسال: {str(e)}")
            return None

    def on_pwm_change(self, channel, value):
        """Handle PWM value change"""
        value = int(float(value))
        self.pwm_values[channel] = value

        # Update display
        voltage = (value / 255.0) * 5.0
        self.pwm_labels[channel].config(text=f"{value} ({voltage:.2f}V)")

        # Send to Arduino
        response = self.send_command(f"SET_PWM,{channel},{value}")

    def set_pwm_value(self, channel, value):
        """Set PWM value directly"""
        self.pwm_vars[channel].set(value)
        self.on_pwm_change(channel, value)

    def update_pwm_display(self):
        """Update PWM display values"""
        for i in range(3):
            value = self.pwm_values[i]
            voltage = (value / 255.0) * 5.0
            self.pwm_labels[i].config(text=f"{value} ({voltage:.2f}V)")

    def single_shoot(self):
        """Send single pulse"""
        response = self.send_command("SINGLE_SHOOT")

    def on_rate_change(self, value):
        """Handle shooting rate change"""
        self.shoot_rate = int(float(value))
        self.rate_label.config(text=f"{self.shoot_rate} Hz")
        response = self.send_command(f"SET_RATE,{self.shoot_rate}")

    def start_continuous_shooting(self):
        """Start continuous shooting"""
        response = self.send_command("START_SHOOT")
        if response and "STARTED" in response:
            self.continuous_shooting = True
            self.shoot_status_label.config(text=f"الحالة: نشط ({self.shoot_rate} Hz)")

    def stop_continuous_shooting(self):
        """Stop continuous shooting"""
        response = self.send_command("STOP_SHOOT")
        if response and "STOPPED" in response:
            self.continuous_shooting = False
            self.shoot_status_label.config(text="الحالة: متوقف")

    def goto_angle(self):
        """Move stepper to specific angle"""
        angle = self.angle_var.get()
        if 0 <= angle < 360:
            response = self.send_command(f"STEPPER_ANGLE,{angle}")
        else:
            messagebox.showerror("خطأ", "الزاوية يجب أن تكون بين 0 و 359")

    def on_speed_change(self, value):
        """Handle stepper speed change"""
        speed = int(float(value))
        self.stepper_data["speed"] = speed
        self.speed_label.config(text=f"{speed} RPM")
        response = self.send_command(f"STEPPER_SPEED,{speed}")

    def stepper_cw(self):
        """Start clockwise rotation"""
        response = self.send_command("STEPPER_CW")

    def stepper_ccw(self):
        """Start counter-clockwise rotation"""
        response = self.send_command("STEPPER_CCW")

    def stepper_stop(self):
        """Stop stepper motor"""
        response = self.send_command("STEPPER_STOP")

    def stepper_reset(self):
        """Reset stepper position"""
        response = self.send_command("STEPPER_RESET")

    def relay_on(self, relay_id):
        """Turn relay on"""
        timer_var = getattr(self, f"timer_{relay_id.lower()}_var")
        timer = timer_var.get()
        response = self.send_command(f"RELAY_{relay_id}_ON,{timer}")

    def relay_off(self, relay_id):
        """Turn relay off"""
        response = self.send_command(f"RELAY_{relay_id}_OFF")

    def manual_refresh(self):
        """Manually refresh all status"""
        if not self.is_connected:
            return

        # Get PWM values
        response = self.send_command("GET_PWM")
        if response and "PWM_VALUES:" in response:
            values = response.split(":")[1].split(",")
            for i, val in enumerate(values[:3]):
                self.pwm_values[i] = int(val)
                self.pwm_vars[i].set(int(val))
            self.update_pwm_display()

        # Get stepper status
        response = self.send_command("GET_STEPPER")
        if response and "STEPPER_STATUS:" in response:
            data = response.split(":")[1].split(",")
            if len(data) >= 3:
                self.stepper_data["angle"] = float(data[0])
                self.stepper_data["speed"] = int(data[1])
                self.stepper_data["mode"] = data[2]
                self.stepper_status_label.config(
                    text=f"الزاوية: {self.stepper_data['angle']:.1f}° | "
                         f"السرعة: {self.stepper_data['speed']} RPM | "
                         f"الوضع: {self.stepper_data['mode']}")

        # Get relay status
        response = self.send_command("GET_RELAY")
        if response and "RELAY_STATUS:" in response:
            data = response.split(":")[1].split(",")
            if len(data) >= 6:
                # Right relay
                self.relay_data["right"]["active"] = data[0] == "1"
                self.relay_data["right"]["timer"] = int(data[1])
                self.relay_data["right"]["remaining"] = int(data[2])

                # Left relay
                self.relay_data["left"]["active"] = data[3] == "1"
                self.relay_data["left"]["timer"] = int(data[4])
                self.relay_data["left"]["remaining"] = int(data[5])

                # Update display
                self.update_relay_display()

    def update_relay_display(self):
        """Update relay status display"""
        for relay_id in ["right", "left"]:
            data = self.relay_data[relay_id]
            status_label = getattr(self, f"relay_{relay_id}_status")

            if data["active"]:
                if data["remaining"] > 0:
                    status_text = f"الحالة: نشط (متبقي: {data['remaining']/1000:.1f}s)"
                else:
                    status_text = "الحالة: نشط (يدوي)"
            else:
                status_text = "الحالة: متوقف"

            status_label.config(text=status_text)

    def toggle_auto_refresh(self):
        """Toggle auto refresh"""
        self.auto_refresh = self.auto_refresh_var.get()

    def auto_refresh_timer(self):
        """Auto refresh timer"""
        if self.auto_refresh and self.is_connected:
            self.manual_refresh()
        self.root.after(2000, self.auto_refresh_timer)  # Every 2 seconds

    def save_arduino_settings(self):
        """Save settings to Arduino EEPROM"""
        response = self.send_command("SAVE")
        if response and "SAVED" in response:
            messagebox.showinfo("نجح", "تم حفظ الإعدادات في Arduino")
        else:
            messagebox.showerror("خطأ", "فشل في حفظ الإعدادات")

    def reset_arduino_settings(self):
        """Reset Arduino to default settings"""
        if messagebox.askyesno("تأكيد", "هل تريد إعادة تعيين جميع الإعدادات للقيم الافتراضية؟"):
            response = self.send_command("RESET")
            if response and "COMPLETE" in response:
                messagebox.showinfo("نجح", "تم إعادة تعيين الإعدادات")
                self.manual_refresh()
            else:
                messagebox.showerror("خطأ", "فشل في إعادة التعيين")

    def load_settings(self):
        """Load GUI settings from file"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r') as f:
                    settings = json.load(f)
                    self.auto_refresh = settings.get('auto_refresh', False)
        except Exception as e:
            self.auto_refresh = False

    def save_settings(self):
        """Save GUI settings to file"""
        try:
            settings = {
                'auto_refresh': self.auto_refresh
            }
            with open(self.settings_file, 'w') as f:
                json.dump(settings, f)
        except Exception as e:
            pass

    def on_closing(self):
        """Handle window closing"""
        self.save_settings()
        if self.is_connected:
            self.disconnect()
        self.root.destroy()

def main():
    root = tk.Tk()
    app = ArduinoController(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
