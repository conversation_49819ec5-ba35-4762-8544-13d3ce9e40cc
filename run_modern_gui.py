#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick launcher for Modern Arduino Controller
مشغل سريع لواجهة Arduino العصرية
"""

import sys
import os
import subprocess
import platform

def check_virtual_env():
    """Check if running in virtual environment"""
    return hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)

def activate_and_run():
    """Activate virtual environment and run the GUI"""
    system = platform.system()
    
    if system == "Windows":
        venv_python = os.path.join("arduino_env", "Scripts", "python.exe")
        activate_script = os.path.join("arduino_env", "Scripts", "activate.bat")
    else:
        venv_python = os.path.join("arduino_env", "bin", "python")
        activate_script = os.path.join("arduino_env", "bin", "activate")
    
    # Check if virtual environment exists
    if not os.path.exists(venv_python):
        print("❌ Virtual environment not found!")
        print("❌ البيئة الافتراضية غير موجودة!")
        print("\nPlease run setup first:")
        print("يرجى تشغيل الإعداد أولاً:")
        if system == "Windows":
            print("   setup_environment.bat")
        else:
            print("   ./setup_linux.sh")
        input("\nPress Enter to exit...")
        return False
    
    # Run the GUI using virtual environment Python
    try:
        subprocess.run([venv_python, "arduino_gui.py"], check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running GUI: {e}")
        print("❌ خطأ في تشغيل الواجهة")
        return False
    except FileNotFoundError:
        print("❌ arduino_gui.py not found!")
        print("❌ ملف arduino_gui.py غير موجود!")
        return False

def main():
    """Main function"""
    print("🚀 Modern Arduino Controller Launcher")
    print("🚀 مشغل واجهة Arduino العصرية")
    print("=" * 50)
    
    # Check if already in virtual environment
    if check_virtual_env():
        print("✅ Running in virtual environment")
        print("✅ يعمل في البيئة الافتراضية")
        
        # Import and run directly
        try:
            import arduino_gui
            arduino_gui.main()
        except ImportError as e:
            print(f"❌ Import error: {e}")
            print("❌ خطأ في الاستيراد")
            print("Please install requirements: pip install pyserial")
            print("يرجى تثبيت المتطلبات: pip install pyserial")
    else:
        print("🔄 Activating virtual environment...")
        print("🔄 تفعيل البيئة الافتراضية...")
        
        if not activate_and_run():
            input("\nPress Enter to exit...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️ Interrupted by user")
        print("⏹️ تم الإيقاف من قبل المستخدم")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print("❌ خطأ غير متوقع")
        input("\nPress Enter to exit...")
