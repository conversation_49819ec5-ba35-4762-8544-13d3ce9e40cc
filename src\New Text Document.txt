#include <Arduino.h>
#include <WiFiS3.h>
#include <aWOT.h>
#include <EEPROM.h>
#include <Stepper.h>

// Pin Definitions
const int RED_LED_PIN = 9;
const int GREEN_LED_PIN = 5;
const int BLUE_LED_PIN = 6;
const int PULSE_PIN = 8;

// Relay Pins (اختر بنات مناسبة غير مستخدمة)
const int RELAY_RIGHT_PIN = 10; 
const int RELAY_LEFT_PIN = 11;

// Stepper Motor Pins
const int STEPPER_PIN_IN1 = 2;
const int STEPPER_PIN_IN2 = 3;
const int STEPPER_PIN_IN3 = 4;
const int STEPPER_PIN_IN4 = 7;

const int STEPS_PER_REVOLUTION_MOTOR = 2048; // For 28BYJ-48
Stepper myStepper(STEPS_PER_REVOLUTION_MOTOR, STEPPER_PIN_IN1, STEPPER_PIN_IN3, STEPPER_PIN_IN2, STEPPER_PIN_IN4);

// Network credentials
const char* ssid = "Mardaw";       // <<<<<<<<<<< YOUR WIFI SSID
const char* password = "Jawad+SH"; // <<<<<<<<<<< YOUR WIFI PASSWORD

WiFiServer wifiServer(80);
Application app;

int currentPwmValues[3] = {0, 0, 0};
long stepperTotalSteps = 0;
float currentStepperAngle = 0.0;
int stepperSpeedRPM = 12; 
enum StepperMode { STEPPER_IDLE, STEPPER_GOTO_INTERNAL, STEPPER_CW, STEPPER_CCW };
StepperMode currentStepperMode = STEPPER_IDLE;
long stepperTargetTotalSteps = 0;

// Relay States and Timers
bool relayRightActive = false;
unsigned long relayRightDeactivationTime = 0; // 0 means manual mode or off
unsigned int relayRightTimerSettingSec = 5;   // Default timer in seconds

bool relayLeftActive = false;
unsigned long relayLeftDeactivationTime = 0;  // 0 means manual mode or off
unsigned int relayLeftTimerSettingSec = 5;    // Default timer in seconds


struct Settings {
  int pwmValues[3];
  int shootRateHz;
  int stepperSpeedRPM_eeprom;
  unsigned int relayRightTimerSec_eeprom; // Timer setting for Right Relay
  unsigned int relayLeftTimerSec_eeprom;  // Timer setting for Left Relay
  unsigned long magic;
};
Settings currentStoredSettings;
const unsigned long EEPROM_MAGIC_NUMBER = 0xABCD1234;
const int EEPROM_SETTINGS_ADDR = 0;

bool continuousShootingActive = false;
int shootRateHz = 1;
unsigned long lastPulseTime = 0;
unsigned long pulseIntervalMillis = 1000;

// ... (الكود السابق لـ C++ يبقى كما هو من التحديث قبل الأخير) ...

const char index_html[] PROGMEM = R"rawliteral(
<!DOCTYPE html>
<html lang="en" id="pageBody">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Arduino Uno R4 WiFi - Controller</title>
  <style>
    :root {
      --body-bg: #f4f4f4; --container-bg: #fff; --text-color: #333; --heading-color: #007bff;
      --card-bg: #f9f9f9; --card-border: #ddd; --button-bg: #007bff; --button-text: #fff;
      --button-hover-bg: #0056b3; --min-max-btn-bg: #6c757d; --min-max-btn-hover-bg: #5a6268;
      --shoot-btn-bg: #ffc107; --shoot-btn-text: #000; --shoot-btn-hover-bg: #e0a800;
      --value-feedback-bg: #e9ecef; --pwm-val-color: #007bff; --volt-val-color: #28a745;
      --save-btn-bg: #28a745; --save-btn-hover-bg: #218838; --reset-btn-bg: #dc3545;
      --reset-btn-hover-bg: #c82333; --app-status-color: #555; --app-status-error-color: red;
      --input-border-color: #ccc; --footer-credits-color: #333;
      --stepper-active-btn-bg: var(--shoot-btn-bg); /* Yellow for active stepper buttons */
    }
    .dark-theme {
      --body-bg: #1e1e1e; --container-bg: #2c2c2c; --text-color: #e0e0e0; --heading-color: #58a6ff;
      --card-bg: #383838; --card-border: #555; --button-bg: #58a6ff; --button-text: #1e1e1e;
      --button-hover-bg: #3081d9; --min-max-btn-bg: #7a8288; --min-max-btn-hover-bg: #676e74;
      --shoot-btn-bg: #ffca2c; --shoot-btn-text: #1e1e1e; --shoot-btn-hover-bg: #f0b300;
      --value-feedback-bg: #4a4a4a; --pwm-val-color: #58a6ff; --volt-val-color: #6ee787;
      --save-btn-bg: #3fb950; --save-btn-hover-bg: #269038; --reset-btn-bg: #f85149;
      --reset-btn-hover-bg: #da3633; --app-status-color: #aaa; --app-status-error-color: #ff8080;
      --input-border-color: #666; --footer-credits-color: #ccc;
      --stepper-active-btn-bg: var(--shoot-btn-bg);
    }
    body { 
      font-family: Arial, Helvetica, sans-serif; margin: 0; background-color: var(--body-bg); color: var(--text-color);
      display: flex; flex-direction: column; align-items: center; padding: 20px; box-sizing: border-box; min-height: 100vh; 
      transition: background-color 0.3s, color 0.3s;
    }
    .main-container { background-color: var(--container-bg); padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); width: 95%; max-width: 750px; transition: background-color 0.3s;}
    h1 { color: var(--heading-color); text-align: center; margin-top: 0; margin-bottom: 25px; }
    .output-card { margin-bottom: 20px; padding: 15px; border: 1px solid var(--card-border); border-radius: 6px; background-color: var(--card-bg); transition: background-color 0.3s, border-color 0.3s;}
    .output-card > label:first-child { 
      font-weight: bold; display: block; margin-bottom: 15px; font-size: 1.1em; 
    }
    .controls { display: flex; align-items: center; gap: 8px; flex-wrap: wrap; }
    .controls button { padding: 7px 10px; border: none; border-radius: 4px; background-color: var(--button-bg); color: var(--button-text); cursor: pointer; font-size: 0.95em; min-width: 40px; text-align: center; transition: background-color 0.2s;}
    .controls button.min-max-btn { background-color: var(--min-max-btn-bg); }
    .controls button.min-max-btn:hover { background-color: var(--min-max-btn-hover-bg); }
    .controls button.shoot-btn { background-color: var(--shoot-btn-bg); color: var(--shoot-btn-text); padding: 10px 25px; font-size: 1.1em;}
    .controls button.shoot-btn:hover { background-color: var(--shoot-btn-hover-bg);}
    .controls button:not(.min-max-btn):not(.shoot-btn):hover { background-color: var(--button-hover-bg); }
    
    .controls input[type="range"], .controls input[type="number"] { 
        flex-grow: 1; min-width: 80px; margin: 5px 0; padding: 6px 8px;
        border: 1px solid var(--input-border-color); border-radius:3px; background-color: var(--container-bg); color: var(--text-color); 
    }
    .controls input[type="range"] { padding:0; cursor: pointer; } 
    .controls input[type="number"] { text-align: center; }

    .value-feedback { display: flex; align-items: center; justify-content: center; padding: 5px; background-color: var(--value-feedback-bg); border-radius: 4px; min-width: 120px; text-align: center; transition: background-color 0.3s;}
    .pwm-val, .stepper-angle-val { font-weight: bold; color: var(--pwm-val-color); }
    .volt-val { font-weight: bold; color: var(--volt-val-color); margin-left: 10px; }
    
    .action-buttons { text-align: center; margin-top: 25px; }
    .action-buttons button { padding: 12px 25px; margin: 5px; font-size: 1.1em; border-radius: 5px; border: none; cursor: pointer; transition: background-color 0.2s;}
    .save-button { background-color: var(--save-btn-bg); color: var(--button-text); } 
    .save-button:hover { background-color: var(--save-btn-hover-bg); }
    .reset-button { background-color: var(--reset-btn-bg); color: var(--button-text); } 
    .reset-button:hover { background-color: var(--reset-btn-hover-bg); }
    
    #appStatus { text-align: center; margin-top: 15px; font-style: italic; color: var(--app-status-color); min-height: 1.5em; }
    .button-group { display: flex; gap: 5px; }
    
    .footer-credits { text-align: center; margin-top: 30px; padding: 10px; font-size: 0.9em; color: var(--footer-credits-color); width: 100%; font-weight: bold;}
    .theme-toggle-button { position: fixed; top: 15px; right: 15px; padding: 8px 12px; background-color: var(--button-bg); color: var(--button-text); border: none; border-radius: 5px; cursor: pointer; font-size: 0.9em; z-index: 1000; transition: background-color 0.2s, color 0.2s;}
    .theme-toggle-button:hover { background-color: var(--button-hover-bg); }
    
    .shoot-rate-control, .stepper-specific-controls { margin-top: 15px; padding-top:15px; border-top: 1px dashed var(--card-border); }
    .shoot-rate-control label, .stepper-specific-controls label { font-weight: normal; margin-right: 10px; }
    .shoot-rate-control input[type="number"], .stepper-specific-controls input[type="number"] { width: 70px; text-align: center; margin-bottom:10px; }
    .shoot-rate-control .controls button, .stepper-specific-controls .controls button { padding: 8px 15px; font-size: 1em; }
    
    .shoot-rate-status, .stepper-status-display { font-style: italic; margin-top: 8px; font-size: 0.9em; }
    .channel-note { font-size: 0.85em; color: var(--text-color); opacity:0.7; margin-top: 8px; text-align: center; }
    
    .stepper-angle-input-group { display:flex; align-items:center; gap:10px; margin-bottom:10px;}
    .stepper-angle-input-group input[type="number"] {width: 80px;}
    .stepper-angle-input-group button { padding: 8px 15px; }

    .relay-controls .relay-timer-control { display: flex; align-items: center; gap: 10px; margin-bottom: 15px; flex-wrap: wrap; }
    .relay-controls .relay-timer-control label { font-weight: normal; margin-bottom: 0; flex-shrink: 0; }
    .relay-controls .relay-timer-control input[type="number"] { width: 80px; flex-grow: 0; padding: 6px 8px; }
    .relay-controls .button-group { display: flex; gap: 10px; justify-content: space-around; margin-top: 15px; }
    .relay-controls .button-group button { flex-grow: 1; padding: 10px 15px; font-size: 0.95em; }
    .relay-controls .relay-status-display { text-align: center; margin-top: 15px; font-size: 0.95em; padding: 8px; background-color: var(--value-feedback-bg); border-radius: 4px; min-height: 1.5em; }

    /* Active state for stepper CW/CCW buttons */
    #stepperBtnCW.active, #stepperBtnCCW.active { background-color: var(--stepper-active-btn-bg); color: var(--shoot-btn-text); }


    @media (max-width: 700px) { 
      .controls { flex-direction: column; align-items: stretch; }
      .value-feedback { margin-top:10px; width:auto; justify-content: space-around; }
      .button-group:not(.relay-controls .button-group) { width: 100%; justify-content: space-around; margin-bottom: 5px; }
      .button-group button:not(.relay-controls .button-group button) { flex-grow: 1; }
      .theme-toggle-button { top: 10px; right: 10px; padding: 6px 10px; }
      .stepper-angle-input-group {flex-direction:column; align-items:stretch;}
      .stepper-angle-input-group input[type="number"] {width:100%; box-sizing: border-box;}
      .stepper-angle-input-group button { width: 100%; margin-top: 5px; }
      .relay-controls .relay-timer-control { flex-direction: column; align-items: stretch; gap: 5px; }
      .relay-controls .relay-timer-control label { text-align: left; margin-bottom: 5px; }
      .relay-controls .relay-timer-control input[type="number"] { width: 100%; box-sizing: border-box; margin-left: 0; margin-bottom: 10px; }
      .relay-controls .button-group { flex-direction: column; gap: 8px; }
      .relay-controls .button-group button { width: 100%; margin-bottom: 5px; }
      .relay-controls .button-group button:last-child { margin-bottom: 0; }
    }
  </style>
</head>
<body>
  <button id="themeToggleBtn" class="theme-toggle-button">Dark Mode</button>
  <div class="main-container">
    <h1>Arduino Uno R4 WiFi - Controller</h1>
    <div id="pwm-outputs-wrapper"></div>
    <div id="digital-outputs-wrapper"></div>
    <div id="stepper-motor-wrapper"></div>
    <div id="relay-control-wrapper"></div>
    <div class="action-buttons">
      <button type="button" class="save-button" onclick="handleSave()">SAVE Settings</button>
      <button type="button" class="reset-button" onclick="handleReset()">RESET to Defaults</button>
    </div>
    <div id="appStatus"></div>
  </div>
  <footer class="footer-credits" id="footerCredits">© HS TEAM</footer>

  <script>
    const NUM_PWM_CHANNELS = 3; 
    const PWM_MIN_VAL_GLOBAL = 0, PWM_MAX_VAL_GLOBAL = 255, VOLTAGE_MAX_OUT_UNO = 5.0;
    const channelNames = [ "(Red - D9)", "(Blue - D6)", "(Green - D5)" ]; 
    let isContinuousShootingActive = false;
    const SHOOT_CONTROL_PIN = 8; 
    const RELAY_RIGHT_PIN_DISPLAY = 10; 
    const RELAY_LEFT_PIN_DISPLAY = 11;  
    const STEPPER_MAX_RPM = 20; 
    let stepperStatusIntervalId = null;
    const STEPPER_STATUS_POLL_INTERVAL_MS = 2000; // Slightly faster poll for stepper
    let relayStatusIntervalId = null;
    const RELAY_STATUS_POLL_INTERVAL_MS = 1000; 


    function getElement(id){ return document.getElementById(id); }
    function calculateVoltageDisplay(pwmVal, maxV){ const v=parseInt(pwmVal,10); return isNaN(v)?"0.00V":((v/PWM_MAX_VAL_GLOBAL)*maxV).toFixed(2)+"V"; }

    function createPwmControls(){ 
      const w=getElement('pwm-outputs-wrapper'); if(!w){return;} w.innerHTML='';
      for(let i=0;i<NUM_PWM_CHANNELS;i++){ 
        const card=document.createElement('div');card.className='output-card';
        const cMaxV = VOLTAGE_MAX_OUT_UNO; const cName = channelNames[i];
        let initVal = PWM_MIN_VAL_GLOBAL, sliMin = PWM_MIN_VAL_GLOBAL, sliMax = PWM_MAX_VAL_GLOBAL;
        card.innerHTML=`<label for="slider-${i}">${cName} (0-${cMaxV.toFixed(1)}V):</label>
            <div class="controls"><div class="button-group">
              <button type="button" class="min-max-btn" onclick="setSpecificPwmValue(${i}, PWM_MIN_VAL_GLOBAL)">MIN</button>
              <button type="button" onclick="adjustValue(${i},-10)">--</button><button type="button" onclick="adjustValue(${i},-1)">-</button></div>
              <input type="range" id="slider-${i}" min="${sliMin}" max="${sliMax}" value="${initVal}" oninput="visualUpdate(${i},this.value)" onchange="serverUpdate(${i},this.value)">
              <div class="value-feedback"><span id="pwm-val-${i}" class="pwm-val">${initVal}</span><span id="volt-val-${i}" class="volt-val">${calculateVoltageDisplay(initVal,cMaxV)}</span></div>
              <div class="button-group"><button type="button" onclick="adjustValue(${i},1)">+</button><button type="button" onclick="adjustValue(${i},10)">++</button>
              <button type="button" class="min-max-btn" onclick="setSpecificPwmValue(${i}, PWM_MAX_VAL_GLOBAL)">MAX</button></div></div>`;
        w.appendChild(card);
      }
    }
    
    function createShootControl(){ 
      const wrapper = getElement('digital-outputs-wrapper'); if(!wrapper){return;}
      wrapper.innerHTML = ''; const shootCard = document.createElement('div'); shootCard.className = 'output-card';
      shootCard.innerHTML = `
        <label>Digital Pulse Output (Uno Pin D${SHOOT_CONTROL_PIN}):</label>
        <div class="controls" style="justify-content: center; margin-bottom:15px;">
          <button type="button" class="shoot-btn" onclick="handleSingleShootPulse()">SINGLE SHOT</button></div>
        <div class="channel-note"><i>Press for a single pulse.</i></div>
        <div class="shoot-rate-control"><div>
            <label for="shootRateInput">Pulses/Second (0-10):</label>
            <input type="number" id="shootRateInput" value="1" min="0" max="10" step="1"></div>
          <div class="controls" style="margin-top:10px; justify-content:center;">
            <button type="button" id="toggleShootingBtn" onclick="toggleContinuousShooting()">START Continuous</button></div>
          <div id="shootRateStatus" class="shoot-rate-status">Status: Inactive</div></div>`;
      wrapper.appendChild(shootCard);
    }

    function createStepperControls() {
        const wrapper = getElement('stepper-motor-wrapper'); if(!wrapper) return;
        wrapper.innerHTML = ''; const stepperCard = document.createElement('div'); stepperCard.className = 'output-card';
        stepperCard.innerHTML = `
            <label>Stepper Motor Control (28BYJ-48):</label>
            <div class="stepper-status-display" style="margin-bottom:15px;">
                Current Angle: <span id="stepperAngleDisplay" class="stepper-angle-val">0.0</span>°
                | Speed: <span id="stepperSpeedDisplay">10</span> RPM
                | Mode: <span id="stepperModeDisplay">IDLE</span></div>
            <div class="stepper-angle-input-group">
                <label for="stepperAngleInput">Target Angle (0-359):</label>
                <input type="number" id="stepperAngleInput" value="0" min="0" max="359" step="1">
                <button type="button" onclick="handleStepperGoToAngle()">Go to Angle</button></div>
            <div class="stepper-specific-controls">
                <label for="stepperSpeedInput">Speed (1-${STEPPER_MAX_RPM} RPM):</label>
                <input type="number" id="stepperSpeedInput" value="10" min="1" max="${STEPPER_MAX_RPM}" step="1"></div>
            <div class="controls" style="margin-top:15px; justify-content:center; gap:15px;">
                <button type="button" id="stepperBtnCW" onclick="handleStepperCommand('CW')">Rotate CW</button>
                <button type="button" id="stepperBtnCCW" onclick="handleStepperCommand('CCW')">Rotate CCW</button>
                <button type="button" id="stepperBtnStop" style="background-color: var(--reset-btn-bg);" onclick="handleStepperCommand('STOP')">STOP</button></div>
             <div class="controls" style="margin-top:10px; justify-content:center;">
                <button type="button" class="min-max-btn" onclick="handleStepperCommand('RESETPOS')">Reset Position to 0°</button></div>`;
        wrapper.appendChild(stepperCard);
    }

    function createRelayControls() {
        const wrapper = getElement('relay-control-wrapper'); if (!wrapper) return;
        wrapper.innerHTML = '';
        const relays = [ { id: 'Right', name: 'RIGHT', pin: RELAY_RIGHT_PIN_DISPLAY }, { id: 'Left', name: 'LEFT', pin: RELAY_LEFT_PIN_DISPLAY } ];
        relays.forEach(relay => {
            const card = document.createElement('div'); card.className = 'output-card relay-controls';
            card.innerHTML = `
                <label>Relay Control: ${relay.name} (Pin D${relay.pin})</label>
                <div class="relay-timer-control">
                    <label for="timer${relay.id}">Timer (seconds, 0 for manual ON):</label>
                    <input type="number" id="timer${relay.id}" value="5" min="0" max="3600" step="1">
                </div>
                <div class="button-group">
                    <button type="button" class="save-button" onclick="handleRelay('${relay.id}', 'On')">ACTIVATE ${relay.name}</button>
                    <button type="button" class="reset-button" onclick="handleRelay('${relay.id}', 'Off')">DEACTIVATE ${relay.name}</button>
                </div>
                <div id="status${relay.id}" class="relay-status-display">Status: OFF</div>`;
            wrapper.appendChild(card);
        });
    }


    function visualUpdate(idx,valStr){ 
        const val=parseInt(valStr,10),sli=getElement(`slider-${idx}`),pwmD=getElement(`pwm-val-${idx}`),voltD=getElement(`volt-val-${idx}`);
        if(pwmD)pwmD.textContent=val; if(voltD) voltD.textContent = calculateVoltageDisplay(val, VOLTAGE_MAX_OUT_UNO);
        if(sli&&sli.value!==valStr)sli.value=valStr;
    }
    function serverUpdate(idx,valStr){const n=channelNames[idx];fetch(`/set?output=${idx}&value=${valStr}`).then(r=>{if(!r.ok)showAppStatus(`Error updating ${n}.`,true);}).catch(e=>showAppStatus("Server connection error.",true));}
    function adjustValue(idx,delta){const s=getElement(`slider-${idx}`);if(!s)return;let cur=parseInt(s.value,10),min=parseInt(s.min,10),max=parseInt(s.max,10);let newVal=Math.max(min,Math.min(max,cur+delta));s.value=newVal;visualUpdate(idx,newVal.toString());serverUpdate(idx,newVal.toString());}
    function setSpecificPwmValue(idx,specVal){const s=getElement(`slider-${idx}`);if(!s)return;let eV=specVal;const min=parseInt(s.min,10),max=parseInt(s.max,10);let finalV=Math.max(min,Math.min(max,parseInt(eV,10)));s.value=finalV;visualUpdate(idx,finalV.toString());serverUpdate(idx,finalV.toString());}
    
    function updateStepperDisplay(data) {
        if (!data) return;
        const angleDisp = getElement('stepperAngleDisplay'), speedDisp = getElement('stepperSpeedDisplay'), modeDisp = getElement('stepperModeDisplay');
        if(angleDisp && data.angle !== undefined) angleDisp.textContent = parseFloat(data.angle).toFixed(1);
        if(speedDisp && data.speed !== undefined) speedDisp.textContent = data.speed;
        if(modeDisp && data.mode !== undefined) modeDisp.textContent = data.mode;
        
        const btnCW=getElement('stepperBtnCW'), btnCCW=getElement('stepperBtnCCW');
        if (btnCW) btnCW.classList.toggle('active', data.mode === 'CW');
        if (btnCCW) btnCCW.classList.toggle('active', data.mode === 'CCW');

        if (data.mode === 'CW' || data.mode === 'CCW' || data.mode === 'GOTO') { 
            if (!stepperStatusIntervalId) {
                console.log("Starting stepper status polling for mode:", data.mode);
                stepperStatusIntervalId = setInterval(fetchStepperStatus, STEPPER_STATUS_POLL_INTERVAL_MS);
            }
        } else { 
            if (stepperStatusIntervalId) {
                console.log("Stopping stepper status polling.");
                clearInterval(stepperStatusIntervalId);
                stepperStatusIntervalId = null;
            }
        }
    }
    function fetchStepperStatus() { 
        if (document.hidden) return; 
        fetch('/getStepperStatus').then(r => r.json()).then(data => updateStepperDisplay(data))
        .catch(e => { console.warn("Stepper status update failed."); if (stepperStatusIntervalId) { clearInterval(stepperStatusIntervalId); stepperStatusIntervalId = null; } });
    }

    function updateRelayDisplay(data) {
        if (!data || !data.right || !data.left) { console.warn("Relay status data incomplete:", data); return; }
        const relaysInfo = [ { id: 'Right', data: data.right }, { id: 'Left', data: data.left } ];
        let anyRelayActiveWithTimer = false;

        relaysInfo.forEach(r => {
            const statusEl = getElement(`status${r.id}`);
            if (statusEl) {
                let statusText = `Status: ${r.data.active ? 'ON' : 'OFF'}`;
                if (r.data.active && r.data.timerSetSec > 0 && r.data.remainingMs > 0) {
                    statusText += ` (Timer: ${(r.data.remainingMs / 1000).toFixed(1)}s left)`;
                    anyRelayActiveWithTimer = true;
                } else if (r.data.active && r.data.timerSetSec == 0) {
                     statusText += " (Manual ON)";
                }
                statusEl.textContent = statusText;
            }
        });

        if (anyRelayActiveWithTimer) {
            if (!relayStatusIntervalId) {
                console.log("Starting relay status polling for timer.");
                relayStatusIntervalId = setInterval(fetchRelayStatus, RELAY_STATUS_POLL_INTERVAL_MS);
            }
        } else {
            if (relayStatusIntervalId) {
                console.log("Stopping relay status polling.");
                clearInterval(relayStatusIntervalId);
                relayStatusIntervalId = null;
            }
        }
    }
    function fetchRelayStatus() {
        if (document.hidden) return;
        fetch('/getRelayStatus').then(r => r.json()).then(data => updateRelayDisplay(data))
        .catch(e => { console.warn("Relay status update failed.", e); if (relayStatusIntervalId) { clearInterval(relayStatusIntervalId); relayStatusIntervalId = null; } });
    }

    function populateInputFromSource(inputId, serverValue, localStorageKey) {
        const inputEl = getElement(inputId);
        if (!inputEl) return;
        const storedValue = localStorage.getItem(localStorageKey);
        if (storedValue !== null) {
            inputEl.value = storedValue;
        } else if (serverValue !== undefined) {
            inputEl.value = serverValue;
            localStorage.setItem(localStorageKey, serverValue);
        }
    }

    function fetchInitialValues(){ 
        showAppStatus("Loading settings from device...", false);
        Promise.all([
            fetch('/getValues').then(r=>r.json()), 
            fetch('/getStepperStatus').then(r=>r.json()),
            fetch('/getRelayStatus').then(r=>r.json())
        ])
        .then(([pwmData, stepperData, relayData]) => {
            // PWM Values
            if(pwmData && pwmData.values){ pwmData.values.forEach((valStr, i) => { const s = getElement(`slider-${i}`); if(s){ s.value = parseInt(valStr,10); visualUpdate(i, s.value); }});}
            
            // Stepper
            updateStepperDisplay(stepperData); 
            populateInputFromSource('stepperSpeedInput', stepperData ? stepperData.speed : undefined, 'stepperSpeed');
            
            // Relays
            updateRelayDisplay(relayData);
            if (relayData) {
                populateInputFromSource('timerRight', relayData.right ? relayData.right.timerSetSec : undefined, 'relayRightTimer');
                populateInputFromSource('timerLeft', relayData.left ? relayData.left.timerSetSec : undefined, 'relayLeftTimer');
            }

            // Shoot Rate (No direct fetch, relies on localStorage or default)
            const shootRateInput = getElement('shootRateInput');
            if (shootRateInput) {
                 const storedShootRate = localStorage.getItem('shootRate');
                 if (storedShootRate !== null) shootRateInput.value = storedShootRate;
                 else localStorage.setItem('shootRate', shootRateInput.value); // Store default if not in LS
            }

            showAppStatus("Settings loaded.", false);
        }).catch(e => {
            showAppStatus("Failed to load settings. Using defaults.", true);
            console.error("Failed to load initial settings:", e);
            // Apply defaults if fetch fails for some elements
            for(let i=0; i < NUM_PWM_CHANNELS; i++){ const s = getElement(`slider-${i}`); if(s){s.value = PWM_MIN_VAL_GLOBAL; visualUpdate(i, s.value); }}
            updateStepperDisplay({angle: 0.0, speed: (localStorage.getItem('stepperSpeed') || 10) , mode: "IDLE"});
            if(getElement('stepperSpeedInput') && !localStorage.getItem('stepperSpeed')) getElement('stepperSpeedInput').value = "10";
            
            const defaultRelayTimer = 5;
            updateRelayDisplay({
                right: { active: false, timerSetSec: (localStorage.getItem('relayRightTimer') || defaultRelayTimer), remainingMs: 0 },
                left: { active: false, timerSetSec: (localStorage.getItem('relayLeftTimer') || defaultRelayTimer), remainingMs: 0 }
            });
            ['Right', 'Left'].forEach(id => {
                const timerInput = getElement(`timer${id}`); if (timerInput && !localStorage.getItem(`relay${id}Timer`)) timerInput.value = defaultRelayTimer;
            });
        });
    }
    
    function handleSingleShootPulse(){
        showAppStatus("Sending single pulse...", false);
        fetch('/singleShoot').then(r=>{if(!r.ok)throw Error(r.statusText);return r.text();})
        .then(t=>showAppStatus("SINGLE SHOT sent.",false))
        .catch(e=>showAppStatus("SINGLE SHOT error.",true));
    }

    function toggleContinuousShooting(){
        const btn=getElement('toggleShootingBtn'),stEl=getElement('shootRateStatus'),rIn=getElement('shootRateInput');
        let rate=parseInt(rIn.value,10);
        if(isNaN(rate)||rate<0||rate>10){showAppStatus("Invalid rate (0-10).",true);rIn.value=localStorage.getItem('shootRate')||1;return;}
        localStorage.setItem('shootRate',rate);

        if(isContinuousShootingActive){
            showAppStatus("Stopping continuous shooting...", false);
            fetch(`/stopShooting`).then(r=>r.text()).then(t=>{
                showAppStatus("Continuous pulsing STOPPED.",false);
                btn.textContent='START Continuous'; btn.style.backgroundColor='';
                stEl.textContent=`Status: Inactive`; isContinuousShootingActive=false;
            }).catch(e=>showAppStatus("Error stopping pulses.",true));
        } else {
            if(rate===0){showAppStatus("Rate is 0, cannot start.",true);return;}
            showAppStatus(`Starting continuous shooting at ${rate} pps...`, false);
            fetch(`/setShootRate?rate=${rate}`).then(r=>{if(!r.ok)throw Error("Set rate failed");return fetch(`/startShooting`);})
            .then(r=>{if(!r.ok) throw Error("Start shooting failed"); return r.text();})
            .then(t=>{
                showAppStatus(`Continuous pulsing STARTED at ${rate} pps.`,false);
                btn.textContent='STOP Continuous'; btn.style.backgroundColor='var(--reset-btn-bg)';
                stEl.textContent=`Status: Active at ${rate} pps`; isContinuousShootingActive=true;
            }).catch(e=>showAppStatus("Error starting pulses: " + e.message,true));
        }
    }
    
    function handleStepperGoToAngle() {
        const angleInput = getElement('stepperAngleInput');
        const angle = parseInt(angleInput.value, 10);
        if (isNaN(angle) || angle < 0 || angle > 359) {showAppStatus("Invalid angle (0-359).", true); return;}
        
        showAppStatus(`Stepper moving to ${angle}°...`, false);
        // Optimistic UI update for mode
        if (getElement('stepperModeDisplay')) getElement('stepperModeDisplay').textContent = "GOTO";
        if (!stepperStatusIntervalId) { // Start polling if not already
             stepperStatusIntervalId = setInterval(fetchStepperStatus, STEPPER_STATUS_POLL_INTERVAL_MS);
        }

        fetch(`/stepperSetAngle?angle=${angle}`)
        .then(r => r.json())
        .then(data => {
            // updateStepperDisplay will handle the final state and polling interval
            updateStepperDisplay(data); 
            showAppStatus(`Stepper reached ${data.angle !== undefined ? parseFloat(data.angle).toFixed(1) : angle}° (Mode: ${data.mode || 'IDLE'}).`, false);
        })
        .catch(e => {
            showAppStatus("Stepper GoToAngle error.", true);
            fetchStepperStatus(); // Try to get current status even on error
        });
    }

    function handleStepperSetSpeed() {
        const speedInput = getElement('stepperSpeedInput');
        const speed = parseInt(speedInput.value, 10);
        if (isNaN(speed) || speed < 1 || speed > STEPPER_MAX_RPM) { 
            showAppStatus(`Invalid speed (1-${STEPPER_MAX_RPM}).`, true); 
            speedInput.value = localStorage.getItem('stepperSpeed') || 10; return;
        }
        localStorage.setItem('stepperSpeed', speed);
        showAppStatus(`Setting stepper speed to ${speed} RPM...`, false);
        fetch(`/stepperSetSpeed?rpm=${speed}`).then(r => r.json()).then(data => {
            showAppStatus(`Speed set to ${speed} RPM.`, false); 
            updateStepperDisplay(data);
        })
        .catch(e => {
            showAppStatus("Stepper SetSpeed error.", true);
            fetchStepperStatus();
        });
    }

    function handleStepperCommand(command) {
        let endpoint = '';
        let optimisticMode = '';
        switch(command.toUpperCase()) {
            case 'CW': endpoint = '/stepperStartCW'; optimisticMode = 'CW'; break;
            case 'CCW': endpoint = '/stepperStartCCW'; optimisticMode = 'CCW'; break;
            case 'STOP': endpoint = '/stepperStop'; optimisticMode = 'IDLE'; break;
            case 'RESETPOS': endpoint = '/stepperResetPos'; optimisticMode = 'IDLE'; break; /* Mode becomes IDLE, angle becomes 0 */
            default: showAppStatus('Unknown stepper command.', true); return;
        }
        
        // Optimistic UI update for mode and button style
        if (getElement('stepperModeDisplay')) getElement('stepperModeDisplay').textContent = optimisticMode;
        const btnCW = getElement('stepperBtnCW'), btnCCW = getElement('stepperBtnCCW');
        if (btnCW) btnCW.classList.toggle('active', optimisticMode === 'CW');
        if (btnCCW) btnCCW.classList.toggle('active', optimisticMode === 'CCW');
        if (optimisticMode === 'CW' || optimisticMode === 'CCW' || (command.toUpperCase() === 'RESETPOS' && optimisticMode === 'IDLE') ) { // Start polling for movement or after reset
             if (!stepperStatusIntervalId) {
                stepperStatusIntervalId = setInterval(fetchStepperStatus, STEPPER_STATUS_POLL_INTERVAL_MS);
             }
        } else if (optimisticMode === 'IDLE' && command.toUpperCase() === 'STOP') { // Stop polling if STOP command
            if (stepperStatusIntervalId) { clearInterval(stepperStatusIntervalId); stepperStatusIntervalId = null; }
        }


        showAppStatus(`Sending stepper command '${command}'...`, false);
        fetch(endpoint).then(r => r.json()).then(data => {
            showAppStatus(`Stepper command '${command}' successful.`, false); 
            updateStepperDisplay(data);
        })
        .catch(e => {
            showAppStatus(`Stepper ${command} error.`, true);
            fetchStepperStatus(); // Attempt to recover/update state
        });
    }

    function handleRelay(relayId, action) {
        const timerInput = getElement(`timer${relayId}`);
        const statusEl = getElement(`status${relayId}`);
        let timerSeconds = 0; 

        if (timerInput) {
            timerSeconds = parseInt(timerInput.value, 10);
            if (isNaN(timerSeconds) || timerSeconds < 0 || timerSeconds > 3600) { 
                showAppStatus(`Invalid timer for ${relayId} (0-3600s).`, true);
                timerInput.value = localStorage.getItem(`relay${relayId}Timer`) || 5; 
                return;
            }
            localStorage.setItem(`relay${relayId}Timer`, timerSeconds);
        }

        let endpoint = `/relay${relayId}${action}`;
        if (action === 'On') { 
            endpoint += `?timer=${timerSeconds}`; 
            if (statusEl) statusEl.textContent = `Status: Activating (Timer: ${timerSeconds}s)...`;
        } else { // Action is 'Off'
            if (statusEl) statusEl.textContent = "Status: Deactivating...";
        }
        // Start polling if activating with a timer, or ensure polling stops if deactivating.
        // updateRelayDisplay after fetch will handle the polling interval correctly.

        fetch(endpoint)
            .then(r => {
                if (!r.ok) {
                     return r.json().then(err => { throw new Error(err.error || `Server error ${r.status}`); });
                }
                return r.json();
            })
            .then(data => {
                showAppStatus(`Relay ${relayId} ${action} command processed.`, false);
                updateRelayDisplay(data); 
            })
            .catch(e => {
                showAppStatus(`Relay ${relayId} ${action} Error: ${e.message}`, true);
                fetchRelayStatus(); // Try to get current status
            });
    }

    function handleSave(){
        showAppStatus("Saving all settings to device...", false);
        // The save endpoint on Arduino saves the current server-side state.
        // Ensure client-side preferences (like relay timers, stepper speed, shoot rate) are sent if they should be part of the save.
        // Currently, these are set on the device when used (e.g., relay ON with timer sets that timer as default).
        // So, a simple /save should suffice to persist those already-set-on-device values.
        fetch('/save').then(r=>{if(!r.ok)throw Error("Save failed");showAppStatus("Settings saved to device.",false);})
        .catch(e=>showAppStatus("Save error: " + e.message ,true));
    }
    
    function proceedWithReset(){
        showAppStatus("Resetting device to defaults...", false);
        fetch('/reset').then(r=>r.json()) // Expecting JSON back, even if simple {status: "ok"}
        .then(data => {
            // Clear relevant localStorage items so fetchInitialValues re-populates from server defaults
            localStorage.removeItem('shootRate');
            localStorage.removeItem('stepperSpeed');
            localStorage.removeItem('relayRightTimer');
            localStorage.removeItem('relayLeftTimer');
            // Other items like PWM slider values are not in LS, they are directly from server.

            isContinuousShootingActive=false; // Reset client-side state tracker
            const toggleBtn = getElement('toggleShootingBtn');
            if(toggleBtn) { toggleBtn.textContent='START Continuous'; toggleBtn.style.backgroundColor=''; }
            const shootStatusEl = getElement('shootRateStatus');
            if(shootStatusEl) shootStatusEl.textContent=`Status: Inactive`;
            
            fetchInitialValues(); // Fetch and display the new defaults from the device
            showAppStatus("Device reset to defaults. Settings loaded.",false);
        }).catch(e=>{
            showAppStatus("Reset error. Please try again.",true);
            console.error("Reset error:", e);
        });
    }

    function handleReset(){
        if(!confirm(`Reset all settings to defaults? This will stop any active movement or relays.`)) return;
        showAppStatus("Stopping active processes for reset...", false);
        const stopActions = [ fetch(`/stepperStop`), fetch(`/relayRightOff`), fetch(`/relayLeftOff`) ];
        if(isContinuousShootingActive) { stopActions.push(fetch(`/stopShooting`)); }
        
        Promise.allSettled(stopActions).finally(() => {
            // Brief pause to allow stop commands to be processed by Arduino before reset command
            setTimeout(proceedWithReset, 500); 
        });
    }

    function showAppStatus(msg,isErr=false, duration){
        const s=getElement('appStatus');
        if(s){
            s.textContent=msg;
            s.style.color=isErr?`var(--app-status-error-color)`:`var(--app-status-color)`;
            if(s.timeoutId) clearTimeout(s.timeoutId); // Clear existing timeout
            s.timeoutId = setTimeout(()=>{if(s.textContent===msg)s.textContent='';}, duration !== undefined ? duration : (isErr?7000:4000));
        }
    }
    function animateFooterRgbWave(){const f=getElement('footerCredits');if(!f)return;let t=0;const spd=0.05;setInterval(()=>{t+=spd;const r=Math.floor(((Math.sin(t*1.0)+1)/2)*255),g=Math.floor(((Math.sin(t*0.7+2)+1)/2)*255),b=Math.floor(((Math.sin(t*0.4+4)+1)/2)*255);f.style.color=`rgb(${r},${g},${b})`;},50);}
    const themeToggleBtn=getElement('themeToggleBtn'),pageBody=getElement('pageBody');function applyTheme(theme){if(theme==='dark'){pageBody.classList.add('dark-theme');if(themeToggleBtn)themeToggleBtn.textContent='Light Mode';}else{pageBody.classList.remove('dark-theme');if(themeToggleBtn)themeToggleBtn.textContent='Dark Mode';}}function toggleTheme(){let cur=localStorage.getItem('theme');cur=(pageBody.classList.contains('dark-theme'))?'light':'dark';localStorage.setItem('theme',cur);applyTheme(cur);}function loadSavedTheme(){const saved=localStorage.getItem('theme');applyTheme(saved?saved:(window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'));}
    
    document.addEventListener('DOMContentLoaded',()=>{
        createPwmControls();createShootControl();createStepperControls();createRelayControls(); 
        fetchInitialValues(); 
        animateFooterRgbWave();
        if(themeToggleBtn)themeToggleBtn.addEventListener('click',toggleTheme);loadSavedTheme();
        
        // Add change listeners to update localStorage for inputs
        const shootRateInput = getElement('shootRateInput');
        if(shootRateInput) shootRateInput.addEventListener('change', (event) => localStorage.setItem('shootRate', event.target.value));
        
        const stepperSpeedInput = getElement('stepperSpeedInput');
        if(stepperSpeedInput) stepperSpeedInput.addEventListener('change', (event) => localStorage.setItem('stepperSpeed', event.target.value));
        // The onchange for stepperSpeedInput in HTML already calls handleStepperSetSpeed, which also saves to LS. This is redundant but harmless.

        ['Right', 'Left'].forEach(id => {
            const timerInput = getElement(`timer${id}`);
            if (timerInput) { 
                timerInput.addEventListener('change', (event) => {
                    localStorage.setItem(`relay${id}Timer`, event.target.value);
                });
            }
        });
    });
  </script>
</body>
</html>
)rawliteral";

// ... (باقي كود C++ يبدأ من هنا، مثل EEPROM Helper Functions, Route Handlers, setup, loop, etc.) ...

// --- EEPROM Helper Functions ---
void saveSettings() {
  Serial.println(F("Saving settings to EEPROM..."));
  for (int i = 0; i < 3; i++) currentStoredSettings.pwmValues[i] = currentPwmValues[i];
  currentStoredSettings.shootRateHz = shootRateHz;
  currentStoredSettings.stepperSpeedRPM_eeprom = stepperSpeedRPM;
  currentStoredSettings.relayRightTimerSec_eeprom = relayRightTimerSettingSec;
  currentStoredSettings.relayLeftTimerSec_eeprom = relayLeftTimerSettingSec;
  currentStoredSettings.magic = EEPROM_MAGIC_NUMBER;
  EEPROM.put(EEPROM_SETTINGS_ADDR, currentStoredSettings);
  Serial.println(F("Settings saved."));
}

void loadSettings() {
  Serial.println(F("Loading settings from EEPROM..."));
  EEPROM.get(EEPROM_SETTINGS_ADDR, currentStoredSettings);
  if (currentStoredSettings.magic != EEPROM_MAGIC_NUMBER) {
    Serial.println(F("EEPROM uninitialized. Loading defaults."));
    for (int i = 0; i < 3; i++) currentPwmValues[i] = 0;
    shootRateHz = 1;
    stepperSpeedRPM = 12; 
    relayRightTimerSettingSec = 5; // Default 5 seconds
    relayLeftTimerSettingSec = 5;  // Default 5 seconds
    saveSettings();
  } else {
    Serial.println(F("Valid settings found."));
    for (int i = 0; i < 3; i++) currentPwmValues[i] = currentStoredSettings.pwmValues[i];
    shootRateHz = currentStoredSettings.shootRateHz;
    stepperSpeedRPM = currentStoredSettings.stepperSpeedRPM_eeprom;
    relayRightTimerSettingSec = currentStoredSettings.relayRightTimerSec_eeprom;
    relayLeftTimerSettingSec = currentStoredSettings.relayLeftTimerSec_eeprom;

    if (stepperSpeedRPM < 1 || stepperSpeedRPM > 20) stepperSpeedRPM = 12; 
    if (relayRightTimerSettingSec > 3600) relayRightTimerSettingSec = 5; // Max 1 hour, default 5s
    if (relayLeftTimerSettingSec > 3600) relayLeftTimerSettingSec = 5;
  }
  if (shootRateHz > 0) pulseIntervalMillis = 1000 / shootRateHz;
  else {pulseIntervalMillis = 0; continuousShootingActive = false;}
  myStepper.setSpeed(stepperSpeedRPM); 
  Serial.print(F("Loaded: PWMs="));
  for(int i=0; i<3; i++) { Serial.print(currentPwmValues[i]); Serial.print(i==2?"":", "); }
  Serial.print(F(" | Rate=")); Serial.print(shootRateHz);
  Serial.print(F(" | Stepper RPM=")); Serial.print(stepperSpeedRPM);
  Serial.print(F(" | Relay Timers (R/L): ")); Serial.print(relayRightTimerSettingSec); Serial.print(F("s/")); Serial.print(relayLeftTimerSettingSec); Serial.println(F("s"));
}

String getStepperModeString() {
    switch(currentStepperMode) {
        case STEPPER_IDLE: return "IDLE"; case STEPPER_GOTO_INTERNAL: return "GOTO";
        case STEPPER_CW: return "CW"; case STEPPER_CCW: return "CCW";
        default: return "UNKNOWN";
    }
}
void updateStepperAngleFromTotal() {
    currentStepperAngle = fmod(((float)stepperTotalSteps / STEPS_PER_REVOLUTION_MOTOR) * 360.0, 360.0);
    if (currentStepperAngle < 0) currentStepperAngle += 360.0;
}
void sendStepperStatus(Response &res) {
    updateStepperAngleFromTotal();
    String json = F("{\"angle\":"); json += String(currentStepperAngle, 1);
    json += F(",\"speed\":"); json += String(stepperSpeedRPM);
    json += F(",\"mode\":\""); json += getStepperModeString(); json += F("\"}");
    res.set("Content-Type", "application/json"); res.print(json);
}

void sendRelayStatus(Response &res) {
    String json = F("{\"right\":{\"active\":"); json += relayRightActive ? "true" : "false";
    json += F(",\"timerSetSec\":"); json += String(relayRightTimerSettingSec);
    json += F(",\"remainingMs\":");
    if (relayRightActive && relayRightDeactivationTime != 0) {
        long remaining = (long)relayRightDeactivationTime - (long)millis();
        json += String(remaining > 0 ? remaining : 0);
    } else {
        json += "0";
    }
    json += F("},\"left\":{\"active\":"); json += relayLeftActive ? "true" : "false";
    json += F(",\"timerSetSec\":"); json += String(relayLeftTimerSettingSec);
    json += F(",\"remainingMs\":");
    if (relayLeftActive && relayLeftDeactivationTime != 0) {
        long remaining = (long)relayLeftDeactivationTime - (long)millis();
        json += String(remaining > 0 ? remaining : 0);
    } else {
        json += "0";
    }
    json += F("}}");
    res.set("Content-Type", "application/json"); res.print(json);
}


// --- Route Handlers ---
void handleRoot(Request &req, Response &res) { res.set("Content-Type", "text/html"); res.printP(index_html); }

void handleSet(Request &req, Response &res) { 
  char o[10],v[10]; 
  if(!req.query("output",o,10)||!req.query("value",v,10)){
    res.status(400); res.print(F("Missing params")); return;
  }
  int out=atoi(o),val=atoi(v); 
  if(out<0||out>=3||val<0||val>255){
    res.status(400); res.print(F("Invalid params")); return;
  }
  currentPwmValues[out]=val; 
  switch(out){
    case 0:analogWrite(RED_LED_PIN,val);break;
    case 1:analogWrite(BLUE_LED_PIN,val);break;
    case 2:analogWrite(GREEN_LED_PIN,val);break;
  }
  res.print(F("OK"));
}

void handleGetValues(Request &req, Response &res) { 
  String js="{\"values\":["; 
  for(int i=0;i<3;i++){js+="\""+String(currentPwmValues[i])+"\"";if(i<2)js+=",";} 
  js+="]}";
  res.set("Content-Type","application/json"); 
  res.print(js);
}

void handleSingleShoot(Request &req,Response &res){digitalWrite(PULSE_PIN,HIGH);delay(50);digitalWrite(PULSE_PIN,LOW);res.print(F("Pulse sent"));}
void handleStartShooting(Request &req,Response &res){continuousShootingActive=true;lastPulseTime=millis();res.print(F("Shooting started"));}
void handleStopShooting(Request &req,Response &res){continuousShootingActive=false;digitalWrite(PULSE_PIN,LOW);res.print(F("Shooting stopped"));}

void handleSetShootRate(Request &req,Response &res){
  char r[10];
  if(!req.query("rate",r,10)){
    res.status(400); res.print(F("Missing rate")); return;
  }
  int rt=atoi(r);
  if(rt<0||rt>10){
    res.status(400); res.print(F("Rate OOB")); return;
  } 
  shootRateHz=rt;
  if(shootRateHz>0)pulseIntervalMillis=1000/shootRateHz;
  else{continuousShootingActive=false;digitalWrite(PULSE_PIN,LOW);pulseIntervalMillis=0;}
  res.print(F("OK"));
}

void handleSaveSettings(Request &req,Response &res){saveSettings();res.print(F("Settings Saved"));}

void handleResetSettings(Request &req, Response &res) {
  for(int i=0;i<3;i++)currentPwmValues[i]=0; 
  shootRateHz=1;pulseIntervalMillis=1000;continuousShootingActive=false;
  currentStepperMode=STEPPER_IDLE;stepperTotalSteps=0;stepperSpeedRPM=12;myStepper.setSpeed(stepperSpeedRPM);updateStepperAngleFromTotal();
  
  // Reset Relays
  digitalWrite(RELAY_RIGHT_PIN, LOW); relayRightActive = false; relayRightDeactivationTime = 0; relayRightTimerSettingSec = 5; // Default 5s
  digitalWrite(RELAY_LEFT_PIN, LOW); relayLeftActive = false; relayLeftDeactivationTime = 0; relayLeftTimerSettingSec = 5;   // Default 5s

  saveSettings(); // This will save new default relay timers too
  analogWrite(RED_LED_PIN,0);analogWrite(BLUE_LED_PIN,0);analogWrite(GREEN_LED_PIN,0);digitalWrite(PULSE_PIN,LOW);
  
  // Client will re-fetch all statuses, so a simple OK is fine or combined status.
  // For consistency with JS expectation of JSON from reset:
  res.set("Content-Type", "application/json");
  res.print(F("{\"status\":\"OK, reset complete\"}"));
}

void handleNotFound(Request &req,Response &res){
    res.status(404); 
    res.print(F("Not found"));
}

// --- Stepper Handlers ---
void handleStepperSetAngle(Request &req, Response &res) {
    char aS[10];
    if(!req.query("angle",aS,10)){ res.status(400); res.print(F("No angle")); return; }
    float tA=atof(aS);
    if(tA<0||tA>=360){ res.status(400); res.print(F("Angle OOB")); return; }
    currentStepperMode=STEPPER_IDLE; updateStepperAngleFromTotal(); float aD=tA-currentStepperAngle;
    if(aD>180.0)aD-=360.0;else if(aD<-180.0)aD+=360.0; 
    long sM=round((aD/360.0)*STEPS_PER_REVOLUTION_MOTOR);
    if(sM!=0){myStepper.step(sM);stepperTotalSteps+=sM;} 
    sendStepperStatus(res);
}
void handleStepperStartCW(Request &req,Response &res){currentStepperMode=STEPPER_CW;sendStepperStatus(res);}
void handleStepperStartCCW(Request &req,Response &res){currentStepperMode=STEPPER_CCW;sendStepperStatus(res);}
void handleStepperStop(Request &req,Response &res){currentStepperMode=STEPPER_IDLE;sendStepperStatus(res);}
void handleStepperSetSpeed(Request &req, Response &res) {
    char rS[10];
    if(!req.query("rpm",rS,10)){ res.status(400); res.print(F("No RPM")); return; }
    int rpm=atoi(rS);
    if(rpm<1||rpm>20){ res.status(400); res.print(F("RPM OOB (1-20)")); return; } 
    stepperSpeedRPM=rpm;myStepper.setSpeed(stepperSpeedRPM);sendStepperStatus(res);
}
void handleStepperResetPos(Request &req,Response &res){currentStepperMode=STEPPER_IDLE;stepperTotalSteps=0;updateStepperAngleFromTotal();sendStepperStatus(res);}
void handleGetStepperStatus(Request &req,Response &res){sendStepperStatus(res);}


// --- Relay Handlers ---
void handleRelayRightOn(Request &req, Response &res) {
    char timerStr[10];
    unsigned int timerDurationSec = 0; // Default to manual ON

    if (req.query("timer", timerStr, 10)) {
        int parsedTimer = atoi(timerStr);
        if (parsedTimer >= 0 && parsedTimer <= 3600) { // 0 means manual ON, max 1 hour
            timerDurationSec = (unsigned int)parsedTimer;
            relayRightTimerSettingSec = timerDurationSec; // Update the stored default for next save/activation
        } else {
            res.status(400); res.print(F("{\"error\":\"Invalid timer value\"}")); return;
        }
    } else { // If no timer query param, use the currently stored setting. If that's 0, it's manual.
        timerDurationSec = relayRightTimerSettingSec;
    }
    
    digitalWrite(RELAY_RIGHT_PIN, HIGH);
    relayRightActive = true;
    Serial.print(F("Relay RIGHT ON. Timer (sec): ")); Serial.println(timerDurationSec);

    if (timerDurationSec > 0) {
        relayRightDeactivationTime = millis() + (unsigned long)timerDurationSec * 1000UL;
    } else {
        relayRightDeactivationTime = 0; // Manual ON, stays on until OFF command or reset
    }
    sendRelayStatus(res);
}

void handleRelayRightOff(Request &req, Response &res) {
    digitalWrite(RELAY_RIGHT_PIN, LOW);
    relayRightActive = false;
    relayRightDeactivationTime = 0; // Clear any active timer
    Serial.println(F("Relay RIGHT OFF."));
    sendRelayStatus(res);
}

void handleRelayLeftOn(Request &req, Response &res) {
    char timerStr[10];
    unsigned int timerDurationSec = 0; 

    if (req.query("timer", timerStr, 10)) {
        int parsedTimer = atoi(timerStr);
        if (parsedTimer >= 0 && parsedTimer <= 3600) {
            timerDurationSec = (unsigned int)parsedTimer;
            relayLeftTimerSettingSec = timerDurationSec; 
        } else {
            res.status(400); res.print(F("{\"error\":\"Invalid timer value\"}")); return;
        }
    } else {
      timerDurationSec = relayLeftTimerSettingSec;
    }

    digitalWrite(RELAY_LEFT_PIN, HIGH);
    relayLeftActive = true;
    Serial.print(F("Relay LEFT ON. Timer (sec): ")); Serial.println(timerDurationSec);

    if (timerDurationSec > 0) {
        relayLeftDeactivationTime = millis() + (unsigned long)timerDurationSec * 1000UL;
    } else {
        relayLeftDeactivationTime = 0; 
    }
    sendRelayStatus(res);
}

void handleRelayLeftOff(Request &req, Response &res) {
    digitalWrite(RELAY_LEFT_PIN, LOW);
    relayLeftActive = false;
    relayLeftDeactivationTime = 0; 
    Serial.println(F("Relay LEFT OFF."));
    sendRelayStatus(res);
}

void handleGetRelayStatus(Request &req, Response &res) {
    sendRelayStatus(res);
}


void printWifiStatus() { 
  Serial.print(F("SSID: ")); Serial.println(WiFi.SSID()); IPAddress ip = WiFi.localIP(); Serial.print(F("IP: ")); Serial.println(ip);
  Serial.print(F("RSSI: ")); Serial.print(WiFi.RSSI()); Serial.println(F(" dBm"));
}
void handleFlashOn(Request &req, Response &res) {
  digitalWrite(PULSE_PIN, HIGH); // افترض أن PULSE_PIN هو بن الفلاش
  Serial.println(F("Flash turned ON"));
  res.print(F("Flash ON"));
}

void handleFlashOff(Request &req, Response &res) {
  digitalWrite(PULSE_PIN, LOW); // افترض أن PULSE_PIN هو بن الفلاش
  Serial.println(F("Flash turned OFF"));
  res.print(F("Flash OFF"));
}
void setup() {
  Serial.begin(115200); unsigned long startT = millis(); while (!Serial && (millis() - startT < 3000));
  Serial.println(F("\nController Start"));
  
  pinMode(RED_LED_PIN,OUTPUT);pinMode(GREEN_LED_PIN,OUTPUT);pinMode(BLUE_LED_PIN,OUTPUT);pinMode(PULSE_PIN,OUTPUT);
  pinMode(RELAY_RIGHT_PIN, OUTPUT); pinMode(RELAY_LEFT_PIN, OUTPUT);
  
  digitalWrite(PULSE_PIN,LOW);
  digitalWrite(RELAY_RIGHT_PIN, LOW); // Ensure relays are off at start
  digitalWrite(RELAY_LEFT_PIN, LOW);

  loadSettings(); // Load after pinModes are set, before applying loaded values

  Serial.print(F("Connecting to ")); Serial.println(ssid); WiFi.begin(ssid, password);
  for(int i=0;i<20&&WiFi.status()!=WL_CONNECTED;i++){delay(500);Serial.print(F("."));}
  
  if(WiFi.status()==WL_CONNECTED){
    Serial.println(F("\nWiFi Connected")); printWifiStatus();
    analogWrite(RED_LED_PIN,currentPwmValues[0]);analogWrite(BLUE_LED_PIN,currentPwmValues[1]);analogWrite(GREEN_LED_PIN,currentPwmValues[2]);
    
    app.get("/",handleRoot);app.get("/set",handleSet);app.get("/getValues",handleGetValues);
    app.get("/singleShoot",handleSingleShoot);app.get("/startShooting",handleStartShooting);app.get("/stopShooting",handleStopShooting);
    app.get("/setShootRate",handleSetShootRate);app.get("/save",handleSaveSettings);app.get("/reset",handleResetSettings);
    
    app.get("/stepperSetAngle",handleStepperSetAngle);app.get("/stepperStartCW",handleStepperStartCW);
    app.get("/stepperStartCCW",handleStepperStartCCW);app.get("/stepperStop",handleStepperStop);
    app.get("/stepperSetSpeed",handleStepperSetSpeed);app.get("/stepperResetPos",handleStepperResetPos);
    app.get("/getStepperStatus",handleGetStepperStatus);

    // Relay routes
    app.get("/relayRightOn", handleRelayRightOn);
    app.get("/relayRightOff", handleRelayRightOff);
    app.get("/relayLeftOn", handleRelayLeftOn);
    app.get("/relayLeftOff", handleRelayLeftOff);
    app.get("/getRelayStatus", handleGetRelayStatus);

    app.notFound(handleNotFound);
    wifiServer.begin(); Serial.println(F("HTTP Server Started"));
  } else { Serial.println(F("\nWiFi Connection Failed."));}
  if(WiFi.status()==WL_CONNECTED){
    Serial.println(F("\nWiFi Connected")); printWifiStatus();
    // ... (باقي الأسطر الموجودة) ...

    // إضافة المسارات الجديدة
    app.get("/flashOn", handleFlashOn);
    app.get("/flashOff", handleFlashOff);

    // ... (باقي تعريف المسارات) ...
    wifiServer.begin(); Serial.println(F("HTTP Server Started"));
  } else { Serial.println(F("\nWiFi Connection Failed."));}
}

static unsigned long pOST=0; static bool pOA=false; const unsigned long pODM=10; 
void loop() {
  if(WiFi.status()==WL_CONNECTED){WiFiClient client=wifiServer.available();if(client.connected()){app.process(&client);client.stop();}}
  
  unsigned long currentTime = millis(); 

  // Continuous shooting logic 
  if(continuousShootingActive && shootRateHz > 0 && pulseIntervalMillis > 0){
    if(!pOA && (currentTime - lastPulseTime >= pulseIntervalMillis)){
      digitalWrite(PULSE_PIN,HIGH);pOA=true;pOST=currentTime;lastPulseTime=currentTime;
    }
  }
  if(pOA){
    if(currentTime - pOST >= pODM){digitalWrite(PULSE_PIN,LOW);pOA=false;}
  }
  if(!continuousShootingActive && pOA){digitalWrite(PULSE_PIN,LOW);pOA=false;}

  // Stepper continuous rotation logic
  if(currentStepperMode==STEPPER_CW){myStepper.step(1);stepperTotalSteps++;}
  else if(currentStepperMode==STEPPER_CCW){myStepper.step(-1);stepperTotalSteps--;}
  
  // Relay timer logic
  if (relayRightActive && relayRightDeactivationTime != 0 && currentTime >= relayRightDeactivationTime) {
      digitalWrite(RELAY_RIGHT_PIN, LOW);
      relayRightActive = false;
      relayRightDeactivationTime = 0; 
      Serial.println(F("Relay RIGHT deactivated by timer."));
  }
  if (relayLeftActive && relayLeftDeactivationTime != 0 && currentTime >= relayLeftDeactivationTime) {
      digitalWrite(RELAY_LEFT_PIN, LOW);
      relayLeftActive = false;
      relayLeftDeactivationTime = 0;
      Serial.println(F("Relay LEFT deactivated by timer."));
  }
}