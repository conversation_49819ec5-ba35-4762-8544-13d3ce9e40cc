# دليل التثبيت - Installation Guide

## Arduino Serial Controller Virtual Environment Setup

### 🚀 Quick Start / البدء السريع

#### Windows:
```bash
# 1. Check system compatibility
python check_python.py

# 2. Setup virtual environment
setup_environment.bat

# 3. Run the application
start_controller.bat
```

#### Linux/Mac:
```bash
# 1. Check system compatibility
python3 check_python.py

# 2. Make scripts executable and setup
chmod +x setup_linux.sh start_controller.sh
./setup_linux.sh

# 3. Run the application
./start_controller.sh
```

---

## 📋 System Requirements / متطلبات النظام

### Minimum Requirements:
- **Python**: 3.6 or higher
- **Operating System**: Windows 7+, Linux, macOS
- **RAM**: 512 MB
- **Storage**: 50 MB free space

### Required Python Modules:
- `tkinter` (usually included with Python)
- `pyserial` (will be installed automatically)

---

## 🔧 Detailed Setup / الإعداد المفصل

### Step 1: Check Python Installation
```bash
# Check if Python is installed
python --version
# or
python3 --version

# Should show Python 3.6 or higher
```

### Step 2: Verify System Compatibility
```bash
# Run the compatibility checker
python check_python.py
```

This will check:
- ✅ Python version compatibility
- ✅ Tkinter availability
- ✅ Pip package manager
- ✅ Virtual environment support

### Step 3: Create Virtual Environment

#### Windows:
```bash
# Automatic setup
setup_environment.bat

# Manual setup (if needed)
python -m venv arduino_env
arduino_env\Scripts\activate.bat
pip install pyserial
```

#### Linux/Mac:
```bash
# Automatic setup
./setup_linux.sh

# Manual setup (if needed)
python3 -m venv arduino_env
source arduino_env/bin/activate
pip install pyserial
```

### Step 4: Run the Application

#### Windows:
```bash
start_controller.bat
```

#### Linux/Mac:
```bash
./start_controller.sh
```

---

## 🐛 Troubleshooting / استكشاف الأخطاء

### Common Issues:

#### 1. Python Not Found
**Problem**: `'python' is not recognized as an internal or external command`

**Solution**:
- Install Python from [python.org](https://python.org)
- Make sure to check "Add Python to PATH" during installation
- Restart command prompt/terminal

#### 2. Tkinter Not Available (Linux)
**Problem**: `ModuleNotFoundError: No module named 'tkinter'`

**Solution**:
```bash
# Ubuntu/Debian
sudo apt-get install python3-tk

# CentOS/RHEL/Fedora
sudo yum install tkinter
# or
sudo dnf install python3-tkinter
```

#### 3. Permission Denied (Linux/Mac)
**Problem**: `Permission denied` when running scripts

**Solution**:
```bash
chmod +x setup_linux.sh start_controller.sh
```

#### 4. Virtual Environment Issues
**Problem**: Virtual environment creation fails

**Solution**:
```bash
# Install venv module (Ubuntu/Debian)
sudo apt-get install python3-venv

# Or use virtualenv instead
pip install virtualenv
virtualenv arduino_env
```

#### 5. Serial Port Access (Linux)
**Problem**: Cannot access serial ports

**Solution**:
```bash
# Add user to dialout group
sudo usermod -a -G dialout $USER
# Then logout and login again
```

---

## 📁 Project Structure / هيكل المشروع

```
arduino_controller/
├── arduino_controller.cpp     # Arduino code
├── arduino_gui.py            # Main GUI application
├── check_python.py           # System compatibility checker
├── setup_environment.bat     # Windows setup script
├── start_controller.bat      # Windows run script
├── setup_linux.sh           # Linux/Mac setup script
├── start_controller.sh       # Linux/Mac run script
├── requirements.txt          # Python dependencies
├── README.md                # Project documentation
├── INSTALLATION_GUIDE.md    # This file
└── arduino_env/             # Virtual environment (created after setup)
    ├── Scripts/             # Windows executables
    ├── bin/                 # Linux/Mac executables
    ├── lib/                 # Python packages
    └── ...
```

---

## 🔄 Virtual Environment Benefits / فوائد البيئة الافتراضية

### Why Use Virtual Environment?

1. **Isolation**: Keeps project dependencies separate
2. **Portability**: Easy to share and reproduce
3. **Clean System**: Doesn't affect system Python
4. **Version Control**: Specific package versions
5. **Easy Cleanup**: Delete folder to remove everything

### Virtual Environment Commands:

#### Activate:
```bash
# Windows
arduino_env\Scripts\activate.bat

# Linux/Mac
source arduino_env/bin/activate
```

#### Deactivate:
```bash
deactivate
```

#### Check Installed Packages:
```bash
pip list
```

---

## 🚀 Advanced Usage / الاستخدام المتقدم

### Manual Package Installation:
```bash
# Activate environment first
# Windows: arduino_env\Scripts\activate.bat
# Linux/Mac: source arduino_env/bin/activate

# Install specific version
pip install pyserial==3.5

# Install from requirements file
pip install -r requirements.txt

# Upgrade package
pip install --upgrade pyserial
```

### Environment Management:
```bash
# Create new environment
python -m venv new_env_name

# Remove environment
# Simply delete the folder
rm -rf arduino_env  # Linux/Mac
rmdir /s arduino_env  # Windows
```

---

## 📞 Support / الدعم

### If you encounter issues:

1. **Check Compatibility**: Run `python check_python.py`
2. **Read Error Messages**: Look for specific error details
3. **Check Logs**: Review terminal output
4. **Restart**: Try restarting terminal/computer
5. **Clean Install**: Delete `arduino_env` folder and run setup again

### System-Specific Notes:

#### Windows:
- Use Command Prompt or PowerShell
- May need to run as Administrator for some operations
- Antivirus might interfere with script execution

#### Linux:
- Use Terminal
- May need `sudo` for system package installation
- Check user permissions for serial ports

#### macOS:
- Use Terminal
- May need to install Xcode Command Line Tools
- Check security settings for script execution

---

## ✅ Verification / التحقق

After successful setup, you should be able to:

1. ✅ Run `start_controller.bat` (Windows) or `./start_controller.sh` (Linux/Mac)
2. ✅ See the Arduino Controller GUI window
3. ✅ Select serial ports from dropdown
4. ✅ Connect to Arduino (when connected)
5. ✅ Control all Arduino functions

---

**© HS TEAM** - Arduino Serial Controller Project
