#!/bin/bash

echo "========================================"
echo "Arduino Controller - Cleanup"
echo "تنظيف بيئة تحكم Arduino"
echo "========================================"
echo

echo "This will remove the virtual environment and all installed packages."
echo "سيتم حذف البيئة الافتراضية وجميع المكتبات المثبتة."
echo
read -p "Are you sure? (y/N) / هل أنت متأكد؟ (y/N): " confirm

if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
    echo "Cleanup cancelled."
    echo "تم إلغاء التنظيف."
    exit 0
fi

echo
echo "Removing virtual environment..."
echo "حذف البيئة الافتراضية..."

if [ -d "arduino_env" ]; then
    rm -rf "arduino_env"
    echo "✓ Virtual environment removed"
    echo "✓ تم حذف البيئة الافتراضية"
else
    echo "Virtual environment not found"
    echo "البيئة الافتراضية غير موجودة"
fi

echo
echo "Removing temporary files..."
echo "حذف الملفات المؤقتة..."

if [ -f "arduino_gui_settings.json" ]; then
    rm "arduino_gui_settings.json"
    echo "✓ Settings file removed"
    echo "✓ تم حذف ملف الإعدادات"
fi

if [ -d "__pycache__" ]; then
    rm -rf "__pycache__"
    echo "✓ Python cache removed"
    echo "✓ تم حذف ذاكرة Python المؤقتة"
fi

find . -name "*.pyc" -delete 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✓ Python compiled files removed"
    echo "✓ تم حذف ملفات Python المترجمة"
fi

echo
echo "========================================"
echo "Cleanup completed!"
echo "تم التنظيف!"
echo "========================================"
echo
echo "To reinstall, run: ./setup_linux.sh"
echo "لإعادة التثبيت، شغل: ./setup_linux.sh"
echo
