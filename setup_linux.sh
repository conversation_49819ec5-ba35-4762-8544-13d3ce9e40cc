#!/bin/bash

echo "========================================"
echo "Arduino Serial Controller Setup (Linux)"
echo "إعداد بيئة تحكم Arduino عبر السيريال"
echo "========================================"
echo

echo "[1/5] Creating virtual environment..."
echo "إنشاء البيئة الافتراضية..."
python3 -m venv arduino_env
if [ $? -ne 0 ]; then
    echo "Error: Failed to create virtual environment"
    echo "خطأ: فشل في إنشاء البيئة الافتراضية"
    exit 1
fi

echo "[2/5] Activating virtual environment..."
echo "تفعيل البيئة الافتراضية..."
source arduino_env/bin/activate

echo "[3/5] Upgrading pip..."
echo "تحديث pip..."
python -m pip install --upgrade pip

echo "[4/5] Installing required packages..."
echo "تثبيت المكتبات المطلوبة..."
pip install pyserial

echo "[5/5] Testing installation..."
echo "اختبار التثبيت..."
python -c "import serial; print('✓ pyserial installed successfully')"
if [ $? -ne 0 ]; then
    echo "Error: Installation failed"
    echo "خطأ: فشل التثبيت"
    exit 1
fi

echo
echo "========================================"
echo "Setup completed successfully!"
echo "تم الإعداد بنجاح!"
echo "========================================"
echo
echo "To run the application:"
echo "لتشغيل التطبيق:"
echo "1. Run: ./start_controller.sh"
echo "2. Or manually: source arduino_env/bin/activate && python arduino_gui.py"
echo
