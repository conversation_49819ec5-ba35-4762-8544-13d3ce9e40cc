@echo off
chcp 65001 >nul
echo ========================================
echo 🚀 Modern Arduino Controller
echo 🚀 واجهة Arduino العصرية
echo ========================================
echo.

REM Check if virtual environment exists
if not exist "arduino_env\Scripts\activate.bat" (
    echo ❌ Virtual environment not found!
    echo ❌ البيئة الافتراضية غير موجودة!
    echo Please run setup_environment.bat first
    echo يرجى تشغيل setup_environment.bat أولاً
    echo.
    pause
    exit /b 1
)

echo 🔄 Activating virtual environment...
echo 🔄 تفعيل البيئة الافتراضية...
call arduino_env\Scripts\activate.bat

echo 🚀 Starting Modern Arduino Controller...
echo 🚀 بدء تشغيل واجهة Arduino العصرية...
echo.
echo ✨ Features: Responsive Design, Dark/Light Themes, Visual Feedback
echo ✨ المميزات: تصميم متجاوب، ثيمات متعددة، تغذية راجعة مرئية
echo.
python arduino_gui.py

echo.
echo 👋 Application closed.
echo 👋 تم إغلاق التطبيق.
pause
