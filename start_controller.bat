@echo off
chcp 65001 >nul
echo ========================================
echo Arduino Serial Controller
echo تحكم Arduino عبر السيريال
echo ========================================
echo.

REM Check if virtual environment exists
if not exist "arduino_env\Scripts\activate.bat" (
    echo Virtual environment not found!
    echo البيئة الافتراضية غير موجودة!
    echo Please run setup_environment.bat first
    echo يرجى تشغيل setup_environment.bat أولاً
    echo.
    pause
    exit /b 1
)

echo Activating virtual environment...
echo تفعيل البيئة الافتراضية...
call arduino_env\Scripts\activate.bat

echo Starting Arduino Controller GUI...
echo بدء تشغيل واجهة تحكم Arduino...
echo.
python arduino_gui.py

echo.
echo Application closed.
echo تم إغلاق التطبيق.
pause
