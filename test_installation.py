#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Installation Test Script
سكريبت اختبار التثبيت
"""

import sys
import os
import subprocess
import platform

def test_virtual_environment():
    """Test if virtual environment is properly set up"""
    print("🏠 Testing Virtual Environment / اختبار البيئة الافتراضية")
    print("=" * 60)
    
    # Check if we're in virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ Running in virtual environment")
        print("✅ يعمل في البيئة الافتراضية")
        print(f"   Virtual env path: {sys.prefix}")
        return True
    else:
        print("❌ Not running in virtual environment")
        print("❌ لا يعمل في البيئة الافتراضية")
        return False

def test_pyserial():
    """Test pyserial installation and functionality"""
    print("\n📡 Testing PySerial / اختبار PySerial")
    print("=" * 60)
    
    try:
        import serial
        import serial.tools.list_ports
        
        print("✅ PySerial imported successfully")
        print("✅ تم استيراد PySerial بنجاح")
        print(f"   Version: {serial.__version__}")
        
        # Test port listing
        ports = list(serial.tools.list_ports.comports())
        print(f"   Available ports: {len(ports)}")
        print(f"   المنافذ المتاحة: {len(ports)}")
        
        for port in ports[:3]:  # Show first 3 ports
            print(f"   - {port.device}: {port.description}")
            
        return True
        
    except ImportError as e:
        print("❌ PySerial import failed")
        print("❌ فشل استيراد PySerial")
        print(f"   Error: {e}")
        return False
    except Exception as e:
        print("❌ PySerial test failed")
        print("❌ فشل اختبار PySerial")
        print(f"   Error: {e}")
        return False

def test_tkinter():
    """Test tkinter functionality"""
    print("\n🖼️ Testing Tkinter / اختبار Tkinter")
    print("=" * 60)
    
    try:
        import tkinter as tk
        from tkinter import ttk, messagebox
        
        print("✅ Tkinter imported successfully")
        print("✅ تم استيراد Tkinter بنجاح")
        
        # Test basic window creation
        root = tk.Tk()
        root.title("Test Window")
        root.geometry("300x200")
        root.withdraw()  # Hide window
        
        # Test ttk widgets
        frame = ttk.Frame(root)
        label = ttk.Label(frame, text="Test Label")
        button = ttk.Button(frame, text="Test Button")
        
        print("✅ Basic widgets created successfully")
        print("✅ تم إنشاء العناصر الأساسية بنجاح")
        
        root.destroy()
        return True
        
    except ImportError as e:
        print("❌ Tkinter import failed")
        print("❌ فشل استيراد Tkinter")
        print(f"   Error: {e}")
        return False
    except Exception as e:
        print("❌ Tkinter test failed")
        print("❌ فشل اختبار Tkinter")
        print(f"   Error: {e}")
        return False

def test_gui_file():
    """Test if arduino_gui.py exists and is valid"""
    print("\n📄 Testing GUI File / اختبار ملف الواجهة")
    print("=" * 60)
    
    if not os.path.exists("arduino_gui.py"):
        print("❌ arduino_gui.py not found")
        print("❌ ملف arduino_gui.py غير موجود")
        return False
    
    print("✅ arduino_gui.py found")
    print("✅ تم العثور على arduino_gui.py")
    
    # Test if file can be imported
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location("arduino_gui", "arduino_gui.py")
        module = importlib.util.module_from_spec(spec)
        
        # Don't execute, just check syntax
        with open("arduino_gui.py", 'r', encoding='utf-8') as f:
            compile(f.read(), "arduino_gui.py", "exec")
            
        print("✅ GUI file syntax is valid")
        print("✅ صيغة ملف الواجهة صحيحة")
        return True
        
    except SyntaxError as e:
        print("❌ GUI file has syntax errors")
        print("❌ ملف الواجهة يحتوي على أخطاء صيغة")
        print(f"   Error: {e}")
        return False
    except Exception as e:
        print("❌ GUI file test failed")
        print("❌ فشل اختبار ملف الواجهة")
        print(f"   Error: {e}")
        return False

def test_arduino_file():
    """Test if arduino_controller.cpp exists"""
    print("\n🔧 Testing Arduino File / اختبار ملف Arduino")
    print("=" * 60)
    
    if not os.path.exists("arduino_controller.cpp"):
        print("❌ arduino_controller.cpp not found")
        print("❌ ملف arduino_controller.cpp غير موجود")
        return False
    
    print("✅ arduino_controller.cpp found")
    print("✅ تم العثور على arduino_controller.cpp")
    
    # Check file size (should be reasonable)
    size = os.path.getsize("arduino_controller.cpp")
    print(f"   File size: {size} bytes")
    print(f"   حجم الملف: {size} بايت")
    
    if size < 1000:
        print("⚠️ Warning: File seems too small")
        print("⚠️ تحذير: الملف يبدو صغيراً جداً")
        return False
    
    return True

def run_gui_test():
    """Try to run the GUI for a quick test"""
    print("\n🚀 Testing GUI Launch / اختبار تشغيل الواجهة")
    print("=" * 60)
    
    try:
        # Import the GUI module
        sys.path.insert(0, '.')
        import arduino_gui
        
        print("✅ GUI module imported successfully")
        print("✅ تم استيراد وحدة الواجهة بنجاح")
        
        # Test class instantiation (without actually showing window)
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # Hide window
        
        # This will test if the class can be created
        app = arduino_gui.ArduinoController(root)
        
        print("✅ GUI class instantiated successfully")
        print("✅ تم إنشاء فئة الواجهة بنجاح")
        
        root.destroy()
        return True
        
    except Exception as e:
        print("❌ GUI test failed")
        print("❌ فشل اختبار الواجهة")
        print(f"   Error: {e}")
        return False

def main():
    """Main test function"""
    print("Arduino Serial Controller - Installation Test")
    print("اختبار تثبيت تحكم Arduino عبر السيريال")
    print("=" * 70)
    print(f"Platform: {platform.system()} {platform.release()}")
    print(f"Python: {sys.version}")
    print("=" * 70)
    
    tests = [
        ("Virtual Environment", test_virtual_environment),
        ("PySerial Library", test_pyserial),
        ("Tkinter GUI", test_tkinter),
        ("GUI File", test_gui_file),
        ("Arduino File", test_arduino_file),
        ("GUI Launch", run_gui_test)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 70)
    print("📋 Test Summary / ملخص الاختبارات")
    print("=" * 70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    print(f"النتائج: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("\n🎉 All tests passed! Installation is successful.")
        print("🎉 جميع الاختبارات نجحت! التثبيت ناجح.")
        print("\nYou can now run the application:")
        print("يمكنك الآن تشغيل التطبيق:")
        if platform.system() == "Windows":
            print("   start_controller.bat")
        else:
            print("   ./start_controller.sh")
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        print("❌ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        print("\nTry running setup again:")
        print("جرب تشغيل الإعداد مرة أخرى:")
        if platform.system() == "Windows":
            print("   setup_environment.bat")
        else:
            print("   ./setup_linux.sh")
    
    print("\n" + "=" * 70)

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit / اضغط Enter للخروج...")
