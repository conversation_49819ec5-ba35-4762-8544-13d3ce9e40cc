# 🚀 Arduino Controller - Modern GUI
# واجهة Arduino العصرية

## 📋 نظرة عامة
واجهة تحكم عصرية وأنيقة لـ Arduino Uno R4 WiFi عبر السيريال مع تصميم متجاوب يعمل على جميع الشاشات.

## 📁 الملفات الأساسية
- **`src/arduino_controller.cpp`** - كود Arduino
- **`arduino_gui.py`** - الواجهة العصرية
- **`run_arduino_gui.py`** - مشغل الواجهة
- **`requirements.txt`** - المتطلبات

## ✨ المميزات العصرية

### 🎨 **تصميم متجاوب**
- **هواتف** 📱: تخطيط مضغوط ومحسن
- **تابلت** 📟: توازن مثالي
- **لابتوب** 💻: استغلال كامل للمساحة

### 🌓 **ثيمات متعددة**
- **وضع داكن** 🌙: مريح للعينين
- **وضع فاتح** ☀️: واضح ومشرق
- **تبديل فوري**: بنقرة واحدة

### 🎛️ **تحكم متطور**
- **PWM ملون** 🎨: معاينة الألوان فورياً
- **مؤشرات مرئية** 💡: حالة الاتصال والعمليات
- **سجل ملون** 📊: تصنيف الرسائل بالألوان
- **أزرار كبيرة** 🔘: سهلة الاستخدام

### 🔧 **الوظائف الأساسية**
- **🎨 PWM**: تحكم RGB (D9,D6,D5)
- **⚡ النبضات**: واحدة أو مستمرة (D8)
- **🔄 المحرك المتدرج**: زاوية ودوران (D2-D7)
- **🔌 المرحلات**: مؤقت تلقائي (D10,D11)
- **💾 الإعدادات**: حفظ في EEPROM

## ⚡ التشغيل السريع

### 🔧 متطلبات الأجهزة
```
Arduino Uno R4 WiFi
- RGB LEDs على المنافذ D9, D6, D5
- محرك متدرج 28BYJ-48 على D2, D3, D4, D7
- مرحلان على D10, D11
- منفذ نبضات على D8
```

### 💻 متطلبات البرمجيات
- **Python 3.6+** (مع tkinter)
- **PySerial** (مكتبة واحدة فقط)

### 🚀 خطوات التشغيل

#### 1. تثبيت المتطلبات
```bash
pip install pyserial
```

#### 2. رفع كود Arduino
```cpp
// رفع src/arduino_controller.cpp إلى Arduino Uno R4
```

#### 3. تشغيل الواجهة
```bash
python run_arduino_gui.py
```

### 🎯 الاستخدام
1. **اختر المنفذ** الصحيح من القائمة
2. **اضبط السرعة** (115200)
3. **اضغط اتصال** 🔗
4. **استمتع بالتحكم** 🎮

## 🎮 طريقة الاستخدام

### 1️⃣ **الاتصال**
- اختر المنفذ من القائمة 📍
- اضبط السرعة (115200) ⚡
- اضغط "🔗 اتصال"

### 2️⃣ **التحكم**
- **🎨 تبويب PWM**: تحكم في الألوان
- **⚡ تبويب النبضات**: إرسال نبضات
- **🔄 تبويب المحرك**: تحريك المحرك
- **🔌 تبويب المرحلات**: تشغيل المرحلات

### 3️⃣ **المراقبة**
- **📊 السجل**: متابعة جميع العمليات
- **🔄 التحديث التلقائي**: كل ثانيتين
- **💾 الحفظ**: إعدادات دائمة

## 🛠️ استكشاف الأخطاء

### ❌ **مشاكل شائعة**
```bash
# مشكلة: pyserial غير مثبت
pip install pyserial

# مشكلة: المنفذ مشغول
# أغلق أي برنامج آخر يستخدم Arduino

# مشكلة: لا توجد استجابة
# تأكد من رفع الكود الصحيح للـ Arduino
```

---
## 👥 **المطورون**
**© HS TEAM** - Arduino Controller Project

🎉 **استمتع بالواجهة العصرية!**
