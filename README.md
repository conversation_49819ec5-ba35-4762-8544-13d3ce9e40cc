# Arduino Serial Controller - تحكم Arduino عبر السيريال

## نظرة عامة
تم تحويل مشروع Arduino Uno R4 WiFi Controller من استخدام WiFi وصفحة ويب إلى نظام تحكم عبر السيريال مع واجهة Tkinter بلغة Python.

## المكونات

### 1. كود <PERSON> (`arduino_controller.cpp`)
- **إزالة WiFi**: تم حذف جميع مكتبات ووظائف WiFi
- **التحكم عبر السيريال**: نظام أوامر نصية عبر Serial
- **نفس الوظائف**: جميع وظائف التحكم والمراقبة الأصلية

### 2. واجهة Python (`arduino_gui.py`)
- **واجهة Tkinter**: واجهة رسومية سهلة الاستخدام
- **اتصال Serial**: تواصل مباشر مع Arduino
- **تحديث فوري**: مراقبة الحالة في الوقت الفعلي

## الوظائف المتاحة

### 🎨 التحكم في PWM (RGB LEDs)
- **3 قنوات**: أحمر (D9), أزرق (D6), أخضر (D5)
- **نطاق القيم**: 0-255 (0-5V)
- **أزرار سريعة**: MIN, 25%, 50%, 75%, MAX
- **عرض الفولتية**: تحويل تلقائي من PWM إلى فولت

### ⚡ نظام النبضات الرقمية
- **نبضة واحدة**: إرسال نبضة فورية
- **نبضات مستمرة**: معدل قابل للتعديل (0-10 Hz)
- **المنفذ**: D8

### 🔄 المحرك المتدرج (28BYJ-48)
- **التحكم بالزاوية**: انتقال دقيق (0-359°)
- **الدوران المستمر**: مع/عكس عقارب الساعة
- **السرعة**: قابلة للتعديل (1-20 RPM)
- **إعادة التعيين**: إعادة الموضع للصفر

### 🔌 المرحلات (Relays)
- **مرحلان**: أيمن (D10), أيسر (D11)
- **مؤقت تلقائي**: 0-3600 ثانية
- **تشغيل يدوي**: بدون مؤقت
- **مراقبة الوقت**: عرض الوقت المتبقي

### 💾 إدارة الإعدادات
- **حفظ في EEPROM**: جميع الإعدادات محفوظة
- **استرجاع تلقائي**: عند إعادة التشغيل
- **إعادة تعيين**: للقيم الافتراضية

## التركيب والتشغيل

### متطلبات الأجهزة
```
Arduino Uno R4 WiFi
- RGB LEDs على المنافذ D9, D6, D5
- محرك متدرج 28BYJ-48 على D2, D3, D4, D7
- مرحلان على D10, D11
- منفذ نبضات على D8
```

### متطلبات البرمجيات
```bash
# تثبيت المكتبات المطلوبة
pip install pyserial
```

### خطوات التشغيل

1. **رفع كود Arduino**:
   ```cpp
   // رفع arduino_controller.cpp إلى Arduino Uno R4
   ```

2. **تشغيل واجهة Python**:
   ```bash
   python arduino_gui.py
   ```

3. **الاتصال**:
   - اختر المنفذ الصحيح
   - اضبط سرعة البيانات (115200)
   - اضغط "اتصال"

## الأوامر المتاحة عبر Serial

### أوامر PWM
```
SET_PWM,channel,value    # تعيين قيمة PWM (channel: 0-2, value: 0-255)
GET_PWM                  # الحصول على قيم PWM الحالية
```

### أوامر النبضات
```
SINGLE_SHOOT            # نبضة واحدة
START_SHOOT             # بدء النبضات المستمرة
STOP_SHOOT              # إيقاف النبضات
SET_RATE,rate           # تعيين معدل النبضات (0-10 Hz)
```

### أوامر المحرك المتدرج
```
STEPPER_CW              # دوران مع عقارب الساعة
STEPPER_CCW             # دوران عكس عقارب الساعة
STEPPER_STOP            # إيقاف الحركة
STEPPER_ANGLE,angle     # انتقال لزاوية محددة (0-359)
STEPPER_SPEED,rpm       # تعيين السرعة (1-20 RPM)
STEPPER_RESET           # إعادة تعيين الموضع
GET_STEPPER             # الحصول على حالة المحرك
```

### أوامر المرحلات
```
RELAY_RIGHT_ON,timer    # تشغيل المرحل الأيمن (timer بالثواني)
RELAY_RIGHT_OFF         # إيقاف المرحل الأيمن
RELAY_LEFT_ON,timer     # تشغيل المرحل الأيسر
RELAY_LEFT_OFF          # إيقاف المرحل الأيسر
GET_RELAY               # الحصول على حالة المرحلات
```

### أوامر الإعدادات
```
SAVE                    # حفظ الإعدادات في EEPROM
RESET                   # إعادة تعيين للقيم الافتراضية
```

## مميزات الواجهة

### 🔄 التحديث التلقائي
- مراقبة الحالة كل ثانيتين
- تحديث فوري للقيم
- إمكانية التحديث اليدوي

### 📝 سجل الأحداث
- تسجيل جميع الأوامر والاستجابات
- طوابع زمنية
- إمكانية مسح السجل

### 🎛️ واجهة سهلة
- تبويبات منظمة
- أزرار واضحة
- عرض القيم بالوحدات المناسبة

### 💾 حفظ الإعدادات
- حفظ تلقائي لإعدادات الواجهة
- استرجاع الإعدادات عند إعادة التشغيل

## الاختلافات عن النسخة الأصلية

### ✅ المحافظ عليه
- جميع وظائف التحكم والمراقبة
- نفس دقة التحكم
- حفظ الإعدادات في EEPROM
- جميع المؤقتات والحالات

### 🔄 المتغير
- **الاتصال**: من WiFi إلى Serial
- **الواجهة**: من صفحة ويب إلى Tkinter
- **البروتوكول**: من HTTP إلى أوامر نصية
- **اللغة**: واجهة ثنائية اللغة (عربي/إنجليزي)

### ➕ المضاف
- سجل أحداث مفصل
- تحديث تلقائي للحالة
- حفظ إعدادات الواجهة
- واجهة أكثر تفاعلية

## استكشاف الأخطاء

### مشاكل الاتصال
```
- تأكد من اختيار المنفذ الصحيح
- تحقق من سرعة البيانات (115200)
- أعد تشغيل Arduino إذا لزم الأمر
```

### مشاكل الأوامر
```
- تحقق من تنسيق الأوامر
- راجع سجل الأحداث للأخطاء
- تأكد من استقرار الاتصال
```

## المطورون
© HS TEAM

---
**ملاحظة**: هذا المشروع يحافظ على جميع وظائف النسخة الأصلية مع تحسين الأداء وسهولة الاستخدام.
