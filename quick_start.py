#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick Start Script for Arduino Serial Controller
سكريبت البدء السريع لتحكم Arduino عبر السيريال
"""

import os
import sys
import platform
import subprocess
import time

def print_header():
    """Print welcome header"""
    print("=" * 70)
    print("🚀 Arduino Serial Controller - Quick Start")
    print("🚀 تحكم Arduino عبر السيريال - البدء السريع")
    print("=" * 70)
    print(f"Platform: {platform.system()} {platform.release()}")
    print(f"Python: {sys.version.split()[0]}")
    print("=" * 70)

def check_files():
    """Check if required files exist"""
    print("\n📁 Checking required files / فحص الملفات المطلوبة...")
    
    required_files = [
        "arduino_gui.py",
        "src/arduino_controller.cpp",
        "requirements.txt"
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n❌ Missing files: {', '.join(missing_files)}")
        print("❌ ملفات مفقودة")
        return False
    
    print("✅ All required files found")
    print("✅ جميع الملفات المطلوبة موجودة")
    return True

def run_compatibility_check():
    """Run compatibility check"""
    print("\n🔍 Running compatibility check / تشغيل فحص التوافق...")
    
    try:
        result = subprocess.run([sys.executable, "check_python.py"], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Compatibility check passed")
            print("✅ فحص التوافق نجح")
            return True
        else:
            print("❌ Compatibility check failed")
            print("❌ فحص التوافق فشل")
            print(result.stdout)
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Compatibility check timed out")
        print("⏰ انتهت مهلة فحص التوافق")
        return False
    except Exception as e:
        print(f"❌ Error running compatibility check: {e}")
        print("❌ خطأ في تشغيل فحص التوافق")
        return False

def setup_environment():
    """Setup virtual environment"""
    print("\n🏗️ Setting up virtual environment / إعداد البيئة الافتراضية...")
    
    system = platform.system()
    
    if system == "Windows":
        script = "setup_environment.bat"
    else:
        script = "./setup_linux.sh"
        # Make executable
        try:
            subprocess.run(["chmod", "+x", "setup_linux.sh"], check=True)
        except:
            pass
    
    try:
        if system == "Windows":
            result = subprocess.run([script], shell=True, timeout=300)
        else:
            result = subprocess.run([script], timeout=300)
        
        if result.returncode == 0:
            print("✅ Virtual environment setup completed")
            print("✅ تم إعداد البيئة الافتراضية")
            return True
        else:
            print("❌ Virtual environment setup failed")
            print("❌ فشل إعداد البيئة الافتراضية")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Setup timed out")
        print("⏰ انتهت مهلة الإعداد")
        return False
    except Exception as e:
        print(f"❌ Error during setup: {e}")
        print("❌ خطأ أثناء الإعداد")
        return False

def test_installation():
    """Test the installation"""
    print("\n🧪 Testing installation / اختبار التثبيت...")
    
    # Check if virtual environment exists
    venv_path = "arduino_env"
    if not os.path.exists(venv_path):
        print("❌ Virtual environment not found")
        print("❌ البيئة الافتراضية غير موجودة")
        return False
    
    # Run test script in virtual environment
    system = platform.system()
    
    if system == "Windows":
        python_exe = os.path.join(venv_path, "Scripts", "python.exe")
    else:
        python_exe = os.path.join(venv_path, "bin", "python")
    
    if not os.path.exists(python_exe):
        print("❌ Python executable not found in virtual environment")
        print("❌ ملف Python التنفيذي غير موجود في البيئة الافتراضية")
        return False
    
    try:
        result = subprocess.run([python_exe, "test_installation.py"], 
                              capture_output=True, text=True, timeout=60)
        
        if "All tests passed" in result.stdout:
            print("✅ Installation test passed")
            print("✅ اختبار التثبيت نجح")
            return True
        else:
            print("❌ Installation test failed")
            print("❌ اختبار التثبيت فشل")
            print("Output:", result.stdout[-500:])  # Last 500 chars
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Installation test timed out")
        print("⏰ انتهت مهلة اختبار التثبيت")
        return False
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        print("❌ خطأ أثناء الاختبار")
        return False

def launch_application():
    """Launch the application"""
    print("\n🚀 Launching application / تشغيل التطبيق...")
    
    system = platform.system()
    
    if system == "Windows":
        script = "start_controller.bat"
        try:
            subprocess.Popen([script], shell=True)
            print("✅ Application launched")
            print("✅ تم تشغيل التطبيق")
            return True
        except Exception as e:
            print(f"❌ Failed to launch: {e}")
            print("❌ فشل التشغيل")
            return False
    else:
        script = "./start_controller.sh"
        try:
            # Make executable
            subprocess.run(["chmod", "+x", "start_controller.sh"], check=True)
            subprocess.Popen([script])
            print("✅ Application launched")
            print("✅ تم تشغيل التطبيق")
            return True
        except Exception as e:
            print(f"❌ Failed to launch: {e}")
            print("❌ فشل التشغيل")
            return False

def main():
    """Main function"""
    print_header()
    
    steps = [
        ("Check Files", check_files),
        ("Compatibility Check", run_compatibility_check),
        ("Setup Environment", setup_environment),
        ("Test Installation", test_installation),
        ("Launch Application", launch_application)
    ]
    
    for i, (step_name, step_func) in enumerate(steps, 1):
        print(f"\n[{i}/{len(steps)}] {step_name}")
        print(f"[{i}/{len(steps)}] {step_name}")
        
        if not step_func():
            print(f"\n❌ Step {i} failed: {step_name}")
            print(f"❌ فشلت الخطوة {i}: {step_name}")
            print("\nPlease check the errors above and try again.")
            print("يرجى مراجعة الأخطاء أعلاه والمحاولة مرة أخرى.")
            break
        
        if i < len(steps):
            print("✅ Step completed, continuing...")
            print("✅ تمت الخطوة، المتابعة...")
            time.sleep(1)
    else:
        print("\n" + "=" * 70)
        print("🎉 Quick start completed successfully!")
        print("🎉 تم البدء السريع بنجاح!")
        print("=" * 70)
        print("\nThe Arduino Controller GUI should now be running.")
        print("واجهة تحكم Arduino يجب أن تكون تعمل الآن.")
        print("\nNext steps:")
        print("الخطوات التالية:")
        print("1. Connect your Arduino Uno R4 WiFi")
        print("2. Select the correct COM port in the GUI")
        print("3. Click 'اتصال' (Connect)")
        print("4. Start controlling your Arduino!")
        print("\n1. اربط Arduino Uno R4 WiFi")
        print("2. اختر المنفذ الصحيح في الواجهة")
        print("3. اضغط 'اتصال'")
        print("4. ابدأ التحكم في Arduino!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️ Quick start interrupted by user")
        print("⏹️ تم إيقاف البدء السريع من قبل المستخدم")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print("❌ خطأ غير متوقع")
    finally:
        input("\nPress Enter to exit / اضغط Enter للخروج...")
