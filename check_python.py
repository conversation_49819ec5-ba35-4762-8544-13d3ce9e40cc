#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python Environment Checker
فاحص بيئة Python
"""

import sys
import platform
import subprocess

def check_python_version():
    """Check Python version"""
    print("🐍 Python Version Check / فحص إصدار Python")
    print("=" * 50)
    
    version = sys.version_info
    print(f"Python Version: {version.major}.{version.minor}.{version.micro}")
    print(f"Platform: {platform.system()} {platform.release()}")
    print(f"Architecture: {platform.machine()}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 6):
        print("❌ Error: Python 3.6+ required")
        print("❌ خطأ: مطلوب Python 3.6 أو أحدث")
        return False
    else:
        print("✅ Python version is compatible")
        print("✅ إصدار Python متوافق")
        return True

def check_tkinter():
    """Check if tkinter is available"""
    print("\n🖼️ Tkinter Check / فحص Tkinter")
    print("=" * 50)
    
    try:
        import tkinter as tk
        print("✅ Tkinter is available")
        print("✅ Tkinter متوفر")
        
        # Test basic functionality
        root = tk.Tk()
        root.withdraw()  # Hide the window
        root.destroy()
        print("✅ Tkinter basic functionality works")
        print("✅ الوظائف الأساسية لـ Tkinter تعمل")
        return True
        
    except ImportError:
        print("❌ Error: Tkinter not found")
        print("❌ خطأ: Tkinter غير موجود")
        print("Install with: sudo apt-get install python3-tk (Ubuntu/Debian)")
        print("أو: yum install tkinter (CentOS/RHEL)")
        return False

def check_pip():
    """Check if pip is available"""
    print("\n📦 Pip Check / فحص Pip")
    print("=" * 50)
    
    try:
        import pip
        print("✅ Pip is available")
        print("✅ Pip متوفر")
        return True
    except ImportError:
        try:
            subprocess.run([sys.executable, "-m", "pip", "--version"], 
                         check=True, capture_output=True)
            print("✅ Pip is available via module")
            print("✅ Pip متوفر عبر الوحدة")
            return True
        except subprocess.CalledProcessError:
            print("❌ Error: Pip not found")
            print("❌ خطأ: Pip غير موجود")
            return False

def check_venv():
    """Check if venv module is available"""
    print("\n🏠 Virtual Environment Check / فحص البيئة الافتراضية")
    print("=" * 50)
    
    try:
        import venv
        print("✅ venv module is available")
        print("✅ وحدة venv متوفرة")
        return True
    except ImportError:
        print("❌ Error: venv module not found")
        print("❌ خطأ: وحدة venv غير موجودة")
        print("Install with: sudo apt-get install python3-venv (Ubuntu/Debian)")
        return False

def main():
    """Main check function"""
    print("Arduino Serial Controller - Environment Check")
    print("فحص بيئة تحكم Arduino عبر السيريال")
    print("=" * 60)
    
    checks = [
        check_python_version(),
        check_tkinter(),
        check_pip(),
        check_venv()
    ]
    
    print("\n" + "=" * 60)
    print("📋 Summary / الملخص")
    print("=" * 60)
    
    if all(checks):
        print("🎉 All checks passed! Ready to setup.")
        print("🎉 جميع الفحوصات نجحت! جاهز للإعداد.")
        print("\nNext steps / الخطوات التالية:")
        if platform.system() == "Windows":
            print("1. Run: setup_environment.bat")
            print("2. Then: start_controller.bat")
        else:
            print("1. Run: chmod +x setup_linux.sh && ./setup_linux.sh")
            print("2. Then: chmod +x start_controller.sh && ./start_controller.sh")
    else:
        print("❌ Some checks failed. Please fix the issues above.")
        print("❌ بعض الفحوصات فشلت. يرجى إصلاح المشاكل أعلاه.")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit / اضغط Enter للخروج...")
