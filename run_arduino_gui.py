#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Arduino Controller Launcher
مشغل بسيط لواجهة Arduino
"""

import sys
import subprocess
import os

def check_requirements():
    """Check if required packages are installed"""
    try:
        import serial
        import tkinter
        return True
    except ImportError as e:
        print(f"❌ Missing required package: {e}")
        print("❌ مكتبة مطلوبة مفقودة")
        print("\nPlease install requirements:")
        print("يرجى تثبيت المتطلبات:")
        print("pip install pyserial")
        return False

def main():
    """Main launcher function"""
    print("🚀 Arduino Controller - Simple Launcher")
    print("🚀 واجهة Arduino - مشغل بسيط")
    print("=" * 50)
    
    # Check if arduino_gui.py exists
    if not os.path.exists("arduino_gui.py"):
        print("❌ arduino_gui.py not found!")
        print("❌ ملف arduino_gui.py غير موجود!")
        input("Press Enter to exit...")
        return
    
    # Check requirements
    if not check_requirements():
        print("\nInstall command:")
        print("أمر التثبيت:")
        print("pip install pyserial")
        input("\nPress Enter to exit...")
        return
    
    print("✅ All requirements satisfied")
    print("✅ جميع المتطلبات متوفرة")
    print("\n🚀 Starting Modern Arduino Controller...")
    print("🚀 بدء تشغيل واجهة Arduino العصرية...")
    print("✨ Features: Dark/Light themes, Responsive design, Visual feedback")
    print("✨ المميزات: ثيمات متعددة، تصميم متجاوب، تغذية راجعة مرئية")

    # Import and run the GUI
    try:
        import arduino_gui
        arduino_gui.main()
    except Exception as e:
        print(f"\n❌ Error starting GUI: {e}")
        print("❌ خطأ في تشغيل الواجهة")
        input("Press Enter to exit...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️ Interrupted by user")
        print("⏹️ تم الإيقاف من قبل المستخدم")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print("❌ خطأ غير متوقع")
        input("Press Enter to exit...")
